#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WGS84经纬度反查中文地址脚本
使用高德地图API进行逆地理编码
"""

import pandas as pd
import requests
import time
import json
import math
from typing import Tuple, Optional

class CoordinateConverter:
    """坐标转换工具类：WGS84 -> GCJ-02"""
    
    # 椭球参数
    a = 6378245.0  # 长半轴
    ee = 0.00669342162296594323  # 偏心率平方
    
    @classmethod
    def wgs84_to_gcj02(cls, lng: float, lat: float) -> Tuple[float, float]:
        """
        WGS84坐标转换为GCJ-02坐标
        """
        if cls._out_of_china(lng, lat):
            return lng, lat
        
        dlat = cls._transform_lat(lng - 105.0, lat - 35.0)
        dlng = cls._transform_lng(lng - 105.0, lat - 35.0)
        
        radlat = lat / 180.0 * math.pi
        magic = math.sin(radlat)
        magic = 1 - cls.ee * magic * magic
        sqrtmagic = math.sqrt(magic)
        
        dlat = (dlat * 180.0) / ((cls.a * (1 - cls.ee)) / (magic * sqrtmagic) * math.pi)
        dlng = (dlng * 180.0) / (cls.a / sqrtmagic * math.cos(radlat) * math.pi)
        
        mglat = lat + dlat
        mglng = lng + dlng
        
        return mglng, mglat
    
    @classmethod
    def _out_of_china(cls, lng: float, lat: float) -> bool:
        """判断是否在中国境外"""
        return not (72.004 <= lng <= 137.8347 and 0.8293 <= lat <= 55.8271)
    
    @classmethod
    def _transform_lat(cls, lng: float, lat: float) -> float:
        """纬度转换"""
        ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + \
              0.1 * lng * lat + 0.2 * math.sqrt(abs(lng))
        ret += (20.0 * math.sin(6.0 * lng * math.pi) + 20.0 * 
                math.sin(2.0 * lng * math.pi)) * 2.0 / 3.0
        ret += (20.0 * math.sin(lat * math.pi) + 40.0 * 
                math.sin(lat / 3.0 * math.pi)) * 2.0 / 3.0
        ret += (160.0 * math.sin(lat / 12.0 * math.pi) + 320 * 
                math.sin(lat * math.pi / 30.0)) * 2.0 / 3.0
        return ret
    
    @classmethod
    def _transform_lng(cls, lng: float, lat: float) -> float:
        """经度转换"""
        ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + \
              0.1 * lng * lat + 0.1 * math.sqrt(abs(lng))
        ret += (20.0 * math.sin(6.0 * lng * math.pi) + 20.0 * 
                math.sin(2.0 * lng * math.pi)) * 2.0 / 3.0
        ret += (20.0 * math.sin(lng * math.pi) + 40.0 * 
                math.sin(lng / 3.0 * math.pi)) * 2.0 / 3.0
        ret += (150.0 * math.sin(lng / 12.0 * math.pi) + 300.0 * 
                math.sin(lng / 30.0 * math.pi)) * 2.0 / 3.0
        return ret


class AmapGeocoder:
    """高德地图逆地理编码类"""
    
    def __init__(self, api_key: str):
        self.api_key = "0b65cd528f00db9f2cb533a1b2fee65f"
        self.base_url = "https://restapi.amap.com/v3/geocode/regeo"
        self.session = requests.Session()
        
    def reverse_geocode(self, lng: float, lat: float, debug: bool = False) -> tuple[Optional[str], Optional[str]]:
        """
        逆地理编码：根据经纬度获取地址
        返回 (道路地址, POI地址) 的元组
        道路地址：包含辅路、立交、隧道、交叉口、方向等详细道路信息
        POI地址：具体到某个地标的地址（如"XX路XX小区"）
        """
        # 转换坐标系：WGS84 -> GCJ-02
        gcj_lng, gcj_lat = CoordinateConverter.wgs84_to_gcj02(lng, lat)

        if debug:
            print(f"  原始坐标 (WGS84): {lng}, {lat}")
            print(f"  转换坐标 (GCJ02): {gcj_lng}, {gcj_lat}")

        params = {
            'key': self.api_key,
            'location': f"{gcj_lng},{gcj_lat}",
            'poitype': '',
            'radius': 1000,
            'extensions': 'all',  # 获取详细信息
            'batch': 'false',
            'roadlevel': 0  # 显示所有道路级别
        }

        if debug:
            print(f"  请求URL: {self.base_url}")
            print(f"  请求参数: {params}")

        try:
            response = self.session.get(self.base_url, params=params, timeout=10)

            if debug:
                print(f"  响应状态码: {response.status_code}")
                print(f"  响应内容: {response.text[:500]}...")

            response.raise_for_status()

            data = response.json()

            if data.get('status') == '1' and data.get('regeocode'):
                regeocode = data['regeocode']
                formatted_address = regeocode.get('formatted_address', '')

                # 提取详细地址组件
                addressComponent = regeocode.get('addressComponent', {})

                # 基本行政区划
                province = addressComponent.get('province', '')
                city = addressComponent.get('city', '')
                district = addressComponent.get('district', '')
                township = addressComponent.get('township', '')

                # 详细道路信息
                streetNumber = addressComponent.get('streetNumber', {})
                street = streetNumber.get('street', '') if streetNumber else ''
                number = streetNumber.get('number', '') if streetNumber else ''

                # 其他道路信息
                roads = regeocode.get('roads', [])
                pois = regeocode.get('pois', [])

                if debug:
                    print(f"  基本地址组件: 省={province}, 市={city}, 区={district}, 街道={township}")
                    print(f"  道路信息: 街道={street}, 门牌号={number}")
                    print(f"  附近道路详情:")
                    for i, road in enumerate(roads[:5]):
                        road_name = road.get('name', '')
                        distance = road.get('distance', 0)
                        direction = road.get('direction', '')
                        print(f"    {i+1}. {road_name} (距离:{distance}m, 方向:{direction})")
                    print(f"  附近POI: {[poi.get('name', '') for poi in pois[:3]]}")
                    print(f"  格式化地址: {formatted_address}")

                # === 1. 构建道路地址（详细道路信息）===
                road_parts = []

                # 处理roads数组，获取道路信息
                if roads and len(roads) > 0:
                    nearby_roads = []
                    special_roads = []

                    for road in roads[:5]:
                        road_name = road.get('name', '').strip()
                        distance_str = road.get('distance', '999')
                        direction = road.get('direction', '')

                        try:
                            distance = float(distance_str)
                        except (ValueError, TypeError):
                            distance = 999.0

                        if road_name and distance < 300:
                            road_info = {
                                'name': road_name,
                                'distance': distance,
                                'direction': direction
                            }

                            # 检查特殊道路类型
                            special_types = ['辅路', '立交', '桥', '隧道', '下穿', '上跨', '匝道', '环路', '高架']
                            if any(stype in road_name for stype in special_types):
                                special_roads.append(road_info)
                            else:
                                nearby_roads.append(road_info)

                    # 按距离排序
                    nearby_roads.sort(key=lambda x: x['distance'])
                    special_roads.sort(key=lambda x: x['distance'])

                    # 构建道路描述
                    if len(nearby_roads) >= 2:
                        # 交叉路口
                        road1 = nearby_roads[0]['name']
                        road2 = nearby_roads[1]['name']
                        road_parts.append(f"{road1}与{road2}交叉口")

                        # 添加特殊道路信息
                        if special_roads:
                            road_parts.append(f"({special_roads[0]['name']})")
                    elif len(nearby_roads) == 1:
                        # 单条道路 + 方向
                        main_road = nearby_roads[0]
                        direction_map = {
                            '东': '东侧', '西': '西侧', '南': '南侧', '北': '北侧',
                            'east': '东侧', 'west': '西侧', 'south': '南侧', 'north': '北侧'
                        }
                        direction_desc = direction_map.get(main_road['direction'], '')

                        if direction_desc and main_road['distance'] > 30:
                            road_parts.append(f"{main_road['name']}{direction_desc}")
                        else:
                            road_parts.append(main_road['name'])

                        # 添加特殊道路信息
                        if special_roads:
                            road_parts.append(f"({special_roads[0]['name']})")
                    elif special_roads:
                        # 只有特殊道路
                        road_parts.append(special_roads[0]['name'])

                # 如果没有从roads获取到信息，使用streetNumber
                if not road_parts and street and street.strip():
                    road_parts.append(street)

                # 如果还没有，使用township
                if not road_parts and township and township.strip():
                    road_parts.append(township)

                # 构建道路地址
                road_address = f"{province}{city}{district}{''.join(road_parts)}" if road_parts else formatted_address

                # === 2. 构建POI地址（具体地标地址）===
                poi_address = ""

                if pois and len(pois) > 0:
                    # 筛选重要POI
                    important_pois = []
                    for poi in pois[:10]:
                        poi_name = poi.get('name', '').strip()
                        poi_type = poi.get('type', '').strip()
                        distance_str = poi.get('distance', '999')

                        try:
                            distance = float(distance_str)
                        except (ValueError, TypeError):
                            distance = 999.0

                        if poi_name and distance < 200:  # 200米内
                            # 过滤不需要的POI
                            skip_types = ['停车场', '公厕', 'ATM', '收费站', '加油站']
                            if not any(skip in poi_name for skip in skip_types):
                                # 优先重要POI类型
                                priority_types = ['住宅区', '小区', '学校', '医院', '商场', '写字楼', '酒店']
                                is_priority = any(ptype in poi_type or ptype in poi_name for ptype in priority_types)

                                important_pois.append({
                                    'name': poi_name,
                                    'type': poi_type,
                                    'distance': distance,
                                    'is_priority': is_priority
                                })

                    # 按优先级和距离排序
                    important_pois.sort(key=lambda x: (not x['is_priority'], x['distance']))

                    if important_pois:
                        closest_poi = important_pois[0]
                        # 获取POI所在的道路
                        poi_road = ""
                        if road_parts:
                            # 使用第一个道路名称（去掉方向和交叉口描述）
                            first_road = road_parts[0]
                            if '与' in first_road and '交叉口' in first_road:
                                poi_road = first_road.split('与')[0]
                            elif '东侧' in first_road or '西侧' in first_road or '南侧' in first_road or '北侧' in first_road:
                                poi_road = first_road.replace('东侧', '').replace('西侧', '').replace('南侧', '').replace('北侧', '')
                            else:
                                poi_road = first_road
                        elif street and street.strip():
                            poi_road = street

                        if poi_road:
                            poi_address = f"{province}{city}{district}{poi_road}{closest_poi['name']}"
                        else:
                            poi_address = f"{province}{city}{district}{closest_poi['name']}"

                if debug:
                    print(f"  道路地址: {road_address}")
                    print(f"  POI地址: {poi_address}")

                return (road_address, poi_address)
            else:
                error_msg = data.get('info', 'Unknown error')
                print(f"API返回错误: status={data.get('status')}, info={error_msg}")
                if debug:
                    print(f"  完整响应: {data}")
                return (None, None)

        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            return (None, None)
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            if debug:
                print(f"  原始响应: {response.text}")
            return (None, None)


def main():
    """主函数"""
    # 高德地图API Key - 请替换为您的实际API Key
    API_KEY = "0b65cd528f00db9f2cb533a1b2fee65f"

    print(f"调试信息: 当前API Key = {API_KEY}")
    print(f"调试信息: API Key长度 = {len(API_KEY)}")

    if not API_KEY or API_KEY == "YOUR_AMAP_API_KEY_HERE":
        print("错误: 请先设置您的高德地图API Key")
        print("请访问 https://console.amap.com/ 申请API Key")
        return
    
    # 读取CSV文件
    input_file = "1h201.9-积水点.csv"
    output_file = "积水点_带地址.csv"
    
    try:
        df = pd.read_csv(input_file)
        print(f"成功读取 {len(df)} 条记录")
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return
    except Exception as e:
        print(f"读取文件错误: {e}")
        return
    
    # 初始化地理编码器
    geocoder = AmapGeocoder(API_KEY)
    
    # 添加两种地址列
    road_addresses = []  # 道路地址
    poi_addresses = []   # POI地址
    
    print("开始逆地理编码...")

    # 先测试第一个点，启用详细调试
    if len(df) > 0:
        first_row = df.iloc[0]
        print(f"\n=== 测试第一个点 (ID: {first_row['id']}) ===")
        test_result = geocoder.reverse_geocode(first_row['lon'], first_row['lat'], debug=True)
        test_road_addr, test_poi_addr = test_result
        print(f"测试结果 - 道路地址: {test_road_addr}")
        print(f"测试结果 - POI地址: {test_poi_addr}")
        print("=" * 50)

        # 询问用户是否继续
        user_input = input("\n测试完成，是否继续处理所有点？(y/n): ").lower().strip()
        if user_input != 'y':
            print("用户取消操作")
            return

    for index, row in df.iterrows():
        lng = row['lon']
        lat = row['lat']
        point_id = row['id']

        print(f"处理第 {index + 1}/{len(df)} 个点 (ID: {point_id})")

        # 获取两种地址信息 (前3个点启用调试)
        debug_mode = index < 3
        result = geocoder.reverse_geocode(lng, lat, debug=debug_mode)
        road_addr, poi_addr = result

        road_addresses.append(road_addr if road_addr else "地址获取失败")
        poi_addresses.append(poi_addr if poi_addr else "")

        # 避免请求过于频繁，添加延时
        time.sleep(0.1)  # 100ms延时

        # 每10个点显示一次进度
        if (index + 1) % 10 == 0:
            print(f"已完成 {index + 1} 个点的地址查询")
    
    # 添加两种地址列到DataFrame
    df['road_address'] = road_addresses  # 道路地址
    df['poi_address'] = poi_addresses    # POI地址
    
    # 保存结果
    try:
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n结果已保存到: {output_file}")
        print(f"共处理 {len(df)} 个点")
        
        # 显示前几条结果
        print("\n前5条结果预览:")
        print(df[['id', 'lon', 'lat', 'road_address', 'poi_address']].head())
        
    except Exception as e:
        print(f"保存文件错误: {e}")


if __name__ == "__main__":
    main()
