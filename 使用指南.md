# 基于大模型的地址匹配解决方案

## 概述

本解决方案使用智谱AI的GLM-4大模型来智能匹配两个数据源中的地址信息：
- **Excel文件**: 包含积水点风险评估数据，有中文地点描述
- **CSV文件**: 包含从地图服务获取的详细地址和经纬度信息

## 技术优势

相比传统的模糊匹配方法，使用大模型有以下优势：

1. **语义理解能力强**: 能理解"下穿隧道"和"交叉口"可能指向同一地点
2. **上下文感知**: 结合行政区、道路名称、POI信息进行综合判断
3. **灵活性高**: 能处理各种表达方式的差异
4. **准确性高**: 基于地理常识和逻辑推理进行匹配

## 文件说明

### 核心文件
- `complete_matching_solution.py`: 完整的匹配解决方案
- `demo_prompt.py`: 基础prompt演示
- `better_demo_prompt.py`: 改进的prompt演示（同行政区匹配）

### 数据文件
- `ocr表格1h201.9mm.xlsx`: 积水点风险评估表（298条记录）
- `积水点_带地址.csv`: 地图地址数据（291条记录）

## 使用步骤

### 1. 环境准备

```bash
pip install pandas openpyxl requests
```

### 2. 获取API密钥

1. 访问 [智谱AI开放平台](https://open.bigmodel.cn/)
2. 注册账号并获取API密钥
3. 确保账户有足够的调用额度

### 3. 基础使用

```python
from complete_matching_solution import AddressMatcherWithLLM
import pandas as pd

# 读取数据
excel_df = pd.read_excel('ocr表格1h201.9mm.xlsx')
csv_df = pd.read_csv('积水点_带地址.csv', encoding='gbk')

# 初始化匹配器
matcher = AddressMatcherWithLLM(api_key='your-api-key-here')

# 处理单个行政区
results = matcher.process_district_batch(excel_df, csv_df, '未央区', batch_size=3)

# 合并数据
final_df = matcher.merge_data_based_on_matches(excel_df, csv_df, results)

# 保存结果
final_df.to_excel('合并结果.xlsx', index=False)
```

### 4. 批量处理所有行政区

```python
# 获取所有行政区
districts = excel_df['行政区'].unique()

all_results = []
for district in districts:
    print(f"处理行政区: {district}")
    district_results = matcher.process_district_batch(
        excel_df, csv_df, district, batch_size=3
    )
    all_results.extend(district_results)
    
    # 避免API调用过于频繁
    time.sleep(2)

# 合并所有结果
final_df = matcher.merge_data_based_on_matches(excel_df, csv_df, all_results)
final_df.to_excel('完整合并结果.xlsx', index=False)
```

## Prompt设计说明

### 核心设计思路

1. **角色定位**: 将AI定位为"专业的地理位置匹配专家"
2. **任务明确**: 清楚说明两组数据的来源和匹配目标
3. **匹配原则**: 提供具体的匹配判断标准
4. **输出格式**: 要求严格的JSON格式输出，便于程序处理

### 匹配原则

1. **地理位置优先**: 重视实际地理对应关系
2. **表达方式灵活**: 理解不同的描述方式
3. **行政区一致**: 确保行政区划匹配
4. **多信息综合**: 结合道路地址和POI信息

### 输出格式

```json
{
    "matches": [
        {
            "excel_index": 1,
            "csv_index": 3,
            "confidence": "high",
            "reason": "匹配理由说明"
        }
    ],
    "unmatched_excel": [2, 4],
    "unmatched_csv": [1, 5],
    "summary": {
        "total_matches": 1,
        "high_confidence": 1,
        "medium_confidence": 0,
        "low_confidence": 0
    }
}
```

## 处理策略

### 1. 分批处理
- 按行政区分组处理，提高匹配准确性
- 每批处理3-5条记录，避免prompt过长
- 控制API调用频率，避免超出限制

### 2. 置信度管理
- **high**: 直接采用匹配结果
- **medium**: 建议人工确认
- **low**: 需要人工重新匹配

### 3. 错误处理
- API调用失败时的重试机制
- JSON解析失败时的降级处理
- 网络异常时的错误记录

## 成本估算

基于智谱AI的定价（以GLM-4为例）：
- 输入token: ¥0.1/1K tokens
- 输出token: ¥0.3/1K tokens

预估单次匹配成本：
- 输入约1000 tokens，输出约500 tokens
- 单次成本约 ¥0.25
- 处理298条记录（按批次5条），约需60次调用
- 总成本约 ¥15

## 质量控制

### 1. 人工抽检
- 对高置信度匹配进行抽样检查
- 对中低置信度匹配进行全面审核

### 2. 数据验证
- 检查经纬度的合理性
- 验证行政区的一致性
- 确认地址描述的相关性

### 3. 结果统计
- 匹配成功率统计
- 各置信度级别分布
- 未匹配记录分析

## 注意事项

1. **API密钥安全**: 不要在代码中硬编码API密钥
2. **调用频率**: 遵守API调用频率限制
3. **数据备份**: 处理前备份原始数据
4. **结果验证**: 对匹配结果进行必要的人工验证

## 扩展功能

### 1. 可视化验证
- 在地图上标注匹配结果
- 显示匹配的置信度
- 高亮未匹配的记录

### 2. 批量导出
- 支持多种格式导出（Excel、CSV、JSON）
- 生成匹配报告
- 导出未匹配记录供人工处理

### 3. 配置优化
- 支持自定义prompt模板
- 可调整批次大小和处理策略
- 支持不同的置信度阈值设置

## 联系支持

如有问题或需要技术支持，请提供：
1. 错误信息和日志
2. 数据样本（脱敏后）
3. 具体的使用场景描述
