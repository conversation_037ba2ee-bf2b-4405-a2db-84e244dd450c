# WGS84经纬度反查中文地址工具

这个脚本可以根据WGS84格式的经纬度坐标，使用高德地图API反查对应的中文地址信息。

## 功能特点

- 支持WGS84坐标系到GCJ-02坐标系的自动转换
- 使用高德地图API进行逆地理编码
- 返回两种不同类型的地址信息：

  **道路地址**：详细的道路信息
  - 交叉路口描述（如"文景路与凤城九路交叉口"）
  - 特殊道路类型（辅路、立交、桥、隧道、下穿、上跨、匝道、环路、高架）
  - 方向描述（东侧、西侧、南侧、北侧等）

  **POI地址**：具体地标地址
  - 结合道路和具体地标（如"文景路海珀兰轩"）
  - 优先显示200米内的重要POI（小区、学校、医院、商场等）
- 批量处理CSV文件中的经纬度数据
- 自动添加请求延时，避免API限流

## 使用前准备

### 1. 申请高德地图API Key

1. 访问 [高德开放平台](https://console.amap.com/)
2. 注册并登录账号
3. 创建应用，选择"Web服务"类型
4. 获取API Key

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 配置API Key

编辑 `reverse_geocoding.py` 文件，将第118行的 `YOUR_AMAP_API_KEY_HERE` 替换为您的实际API Key：

```python
API_KEY = "您的高德地图API_KEY"
```

### 2. 运行脚本

```bash
python reverse_geocoding.py
```

### 3. 查看结果

脚本会读取 `1h201.9-积水点.csv` 文件，并生成 `积水点_带地址.csv` 文件，包含原始数据和对应的地址信息。

## 输入文件格式

CSV文件应包含以下列：
- `lon`: 经度 (WGS84坐标系)
- `lat`: 纬度 (WGS84坐标系)  
- `id`: 点位ID

## 输出文件格式

输出的CSV文件会在原有列的基础上增加：
- `road_address`: 道路地址（包含交叉路口、特殊道路类型、方向等详细道路信息）
- `poi_address`: POI地址（结合道路和具体地标的完整地址）

## 注意事项

1. **API配额限制**: 高德地图API有每日调用次数限制，请根据您的套餐合理使用
2. **请求频率**: 脚本已添加100ms延时，避免请求过于频繁
3. **坐标系转换**: 脚本会自动将WGS84坐标转换为高德地图使用的GCJ-02坐标系
4. **错误处理**: 如果某个点的地址获取失败，会在对应位置标记"地址获取失败"

## 常见问题

### Q: API Key无效怎么办？
A: 请检查API Key是否正确，以及是否已开通"逆地理编码"服务。

### Q: 部分地址获取失败怎么办？
A: 可能是网络问题或坐标超出服务范围，可以重新运行脚本处理失败的点。

### Q: 如何提高处理速度？
A: 可以适当减少延时时间，但要注意不要超过API的频率限制。

## 技术说明

- **坐标转换算法**: 使用标准的WGS84到GCJ-02转换算法
- **API接口**: 使用高德地图逆地理编码API v3
- **地址精度**: 返回到街道级别的地址信息
