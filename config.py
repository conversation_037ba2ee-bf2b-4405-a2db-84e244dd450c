# 智谱AI配置文件
# 请在这里设置您的API密钥

# 智谱AI API密钥
# 获取地址: https://open.bigmodel.cn/
ZHIPU_API_KEY = "808f11f50509413b8ad670efab93789e.gb6i47sSJOift3Ao"

# 模型配置
MODEL_NAME = "glm-4-flash"  # 使用可用的模型

# API配置
BASE_URL = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
TIMEOUT = 60  # 增加超时时间到60秒
TEMPERATURE = 0.1
MAX_TOKENS = 4096

# 处理配置
BATCH_SIZE = 3  # 每批处理的Excel记录数
CSV_CANDIDATES = 5  # 每批CSV候选记录数
DELAY_BETWEEN_CALLS = 1  # API调用间隔（秒）
MAX_DISTRICTS = None  # 限制处理的行政区数量，None表示处理所有
ENABLE_PREVIEW = False  # 是否启用预览模式（只处理少量数据）

# 文件配置
EXCEL_FILE = "ocr表格1h201.9mm.xlsx"
CSV_FILE = "积水点_带地址.csv"
OUTPUT_FILE = "匹配结果.xlsx"
