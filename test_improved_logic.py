import pandas as pd
import json
import requests
import time
import config
from datetime import datetime

def test_improved_matching_logic():
    """测试改进的匹配逻辑"""
    
    print("=== 测试改进的匹配逻辑 ===")
    print("🔍 验证：每个Excel记录与该行政区的所有CSV记录进行匹配")
    
    # 读取数据
    try:
        excel_df = pd.read_excel(config.EXCEL_FILE)
        csv_df = pd.read_csv(config.CSV_FILE, encoding='gbk')
        print(f"✅ 数据加载成功: Excel {len(excel_df)} 条, CSV {len(csv_df)} 条")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 选择一个数据较多的行政区进行测试
    district = '未央区'
    
    excel_district = excel_df[excel_df['行政区'] == district]
    csv_district = csv_df[csv_df['dist'] == district]
    
    print(f"\n📊 {district}数据统计:")
    print(f"   Excel记录: {len(excel_district)} 条")
    print(f"   CSV记录: {len(csv_district)} 条")
    
    # 选择少量Excel记录进行测试
    test_excel = excel_district.head(2)
    
    print(f"\n📋 测试Excel记录:")
    for i, row in test_excel.iterrows():
        print(f"  {i+1}: {row['风险点']}")
    
    print(f"\n📍 所有CSV记录 (共{len(csv_district)}条):")
    for i, row in csv_district.head(10).iterrows():  # 只显示前10条
        print(f"  {i+1}: {row['road_address']} - {row['poi_address']}")
    if len(csv_district) > 10:
        print(f"  ... 还有{len(csv_district)-10}条记录")
    
    # 构建匹配prompt
    excel_records = []
    for idx, row in test_excel.iterrows():
        excel_records.append({
            'index': len(excel_records) + 1,
            'point': row['风险点']
        })
    
    csv_records = []
    for idx, row in csv_district.iterrows():
        csv_records.append({
            'index': len(csv_records) + 1,
            'road': row['road_address'],
            'poi': row['poi_address'],
            'lon': row['lon'],
            'lat': row['lat']
        })
    
    # 创建prompt
    prompt = create_comprehensive_prompt(excel_records, csv_records, district)
    
    print(f"\n📝 Prompt统计:")
    print(f"   Excel记录数: {len(excel_records)}")
    print(f"   CSV记录数: {len(csv_records)}")
    print(f"   Prompt长度: {len(prompt)} 字符")
    
    # 估算成本
    estimated_cost = 0.25  # 单次API调用成本
    print(f"💰 本次测试成本: 约 ¥{estimated_cost:.2f}")
    
    # 确认继续
    user_input = input(f"\n是否继续测试？(y/n): ").lower().strip()
    if user_input != 'y':
        print("❌ 用户取消")
        return
    
    # 调用API
    result = call_matching_api(prompt)
    
    if 'error' in result:
        print(f"❌ API调用失败: {result['error']}")
        return
    
    # 显示结果
    print(f"\n=== 匹配结果 ===")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 分析结果
    if 'matches' in result and result['matches']:
        print(f"\n=== 匹配详情 ===")
        for match in result['matches']:
            excel_idx = match['excel_index'] - 1
            csv_idx = match['csv_index'] - 1
            
            if excel_idx < len(excel_records) and csv_idx < len(csv_records):
                excel_record = excel_records[excel_idx]
                csv_record = csv_records[csv_idx]
                
                print(f"\n🎯 匹配 {match['excel_index']} -> {match['csv_index']}:")
                print(f"   📍 Excel: {excel_record['point']}")
                print(f"   🗺️  CSV: {csv_record['road']}")
                print(f"   🏢 POI: {csv_record['poi']}")
                print(f"   📍 坐标: ({csv_record['lon']}, {csv_record['lat']})")
                print(f"   🎯 置信度: {match['confidence']}")
                print(f"   💭 理由: {match['reason']}")
    else:
        print("❌ 没有找到匹配项")
    
    # 保存测试结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    test_file = f'改进逻辑测试_{district}_{timestamp}.json'
    
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump({
            'district': district,
            'excel_count': len(excel_records),
            'csv_count': len(csv_records),
            'prompt': prompt,
            'result': result,
            'test_time': timestamp
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试结果已保存: {test_file}")
    
    # 验证逻辑
    print(f"\n=== 逻辑验证 ===")
    print(f"✅ 每个Excel记录都与该行政区的所有{len(csv_records)}条CSV记录进行了匹配")
    print(f"✅ 这是正确的匹配逻辑")
    
    if 'matches' in result:
        match_count = len(result['matches'])
        print(f"📊 找到 {match_count} 个匹配项")
        
        # 检查匹配的CSV索引范围
        if result['matches']:
            csv_indices = [m['csv_index'] for m in result['matches']]
            print(f"📍 匹配的CSV记录索引: {csv_indices}")
            print(f"📍 CSV索引范围: 1-{len(csv_records)}")
            
            # 验证索引是否在合理范围内
            valid_indices = all(1 <= idx <= len(csv_records) for idx in csv_indices)
            if valid_indices:
                print(f"✅ 所有匹配索引都在有效范围内")
            else:
                print(f"❌ 发现无效的匹配索引")

def create_comprehensive_prompt(excel_records, csv_records, district):
    """创建全面匹配的prompt"""
    
    prompt = f"""你是专业的地理位置匹配专家。请判断以下{district}的积水点与地图地址的匹配关系。

**重要说明：**
- 每个Excel记录需要在所有{len(csv_records)}条CSV记录中寻找最佳匹配
- 只有高度确信是同一地点时才匹配
- 如果没有合适的匹配，请在unmatched中标出

**积水点数据：**
"""
    
    for record in excel_records:
        prompt += f"{record['index']}. {record['point']}\n"
    
    prompt += f"\n**地图地址数据（共{len(csv_records)}条）：**\n"
    
    for record in csv_records:
        prompt += f"{record['index']}. {record['road']} - {record['poi']}\n"
    
    prompt += f"""
**匹配原则：**
1. 优先考虑地理位置的实际对应关系
2. 理解"下穿"、"立交"、"交叉口"等不同表达方式
3. 只有高度确信是同一地点时才匹配

**返回JSON格式：**
```json
{{
    "matches": [
        {{"excel_index": 1, "csv_index": 15, "confidence": "high", "reason": "匹配理由"}}
    ],
    "unmatched_excel": [2],
    "unmatched_csv": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, ...]
}}
```

只返回JSON，不要其他文字。"""

    return prompt

def call_matching_api(prompt):
    """调用匹配API"""
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.ZHIPU_API_KEY}"
    }
    
    data = {
        "model": "glm-4-flash",
        "messages": [
            {
                "role": "system",
                "content": "你是专业的地理位置匹配专家。请仔细分析所有地址信息，给出准确的匹配结果。严格按照JSON格式返回。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 2000
    }
    
    try:
        print("🚀 正在调用API...")
        start_time = time.time()
        
        response = requests.post(
            config.BASE_URL,
            headers=headers,
            json=data,
            timeout=60
        )
        
        end_time = time.time()
        print(f"📡 响应时间: {end_time - start_time:.2f} 秒")
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result_data = response.json()
            content = result_data['choices'][0]['message']['content']
            
            print(f"✅ API调用成功!")
            print(f"📝 返回内容长度: {len(content)} 字符")
            
            # 解析JSON
            json_content = content.strip()
            if json_content.startswith('```json'):
                json_content = json_content[7:]
            if json_content.endswith('```'):
                json_content = json_content[:-3]
            json_content = json_content.strip()
            
            return json.loads(json_content)
        else:
            return {"error": f"API调用失败: {response.status_code} - {response.text}"}
            
    except Exception as e:
        return {"error": str(e)}

if __name__ == "__main__":
    test_improved_matching_logic()
