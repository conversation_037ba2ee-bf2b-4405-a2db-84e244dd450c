import pandas as pd

def create_matching_prompt(excel_records, csv_records):
    """
    创建地址匹配的prompt
    """
    
    prompt = """你是一个专业的地理位置匹配专家。请帮我判断以下两组地址数据中哪些是指向同一个地点的。

**任务说明：**
- 第一组数据来自积水点风险评估表，包含风险点描述
- 第二组数据来自地图服务，包含详细的道路地址和POI地址
- 请判断第一组中的每个风险点是否与第二组中的某个地点匹配

**匹配原则：**
1. 优先考虑地理位置的实际对应关系，而不是文字的完全一致
2. 考虑道路交叉口、下穿隧道、立交桥等交通设施的描述差异
3. 注意同一地点可能有不同的表达方式（如"下穿"vs"交叉口"）
4. 考虑行政区划的一致性
5. POI地址可以作为辅助判断依据

**第一组数据（风险点）：**
"""
    
    for i, record in enumerate(excel_records, 1):
        prompt += f"{i}. 行政区：{record['行政区']} | 风险点：{record['风险点']}\n"
    
    prompt += "\n**第二组数据（地图地址）：**\n"
    
    for i, record in enumerate(csv_records, 1):
        prompt += f"{i}. 行政区：{record['dist']} | 道路地址：{record['road_address']} | POI地址：{record['poi_address']} | 坐标：({record['lon']}, {record['lat']})\n"
    
    prompt += """
**请按以下JSON格式返回匹配结果：**
```json
{
    "matches": [
        {
            "excel_index": 1,
            "csv_index": 3,
            "confidence": "high",
            "reason": "两者都描述了同一个道路交叉口，只是表达方式略有不同"
        }
    ],
    "unmatched_excel": [2, 4],
    "unmatched_csv": [1, 5],
    "summary": {
        "total_matches": 1,
        "high_confidence": 1,
        "medium_confidence": 0,
        "low_confidence": 0
    }
}
```

**字段说明：**
- excel_index: 第一组数据的序号
- csv_index: 第二组数据的序号  
- confidence: 匹配置信度 ("high", "medium", "low")
- reason: 匹配理由
- unmatched_excel: 第一组中未匹配的记录序号
- unmatched_csv: 第二组中未匹配的记录序号

请仔细分析每个地点的特征，给出准确的匹配结果。"""

    return prompt

def main():
    # 读取数据
    excel_df = pd.read_excel('ocr表格1h201.9mm.xlsx')
    csv_df = pd.read_csv('积水点_带地址.csv', encoding='gbk')
    
    print(f"Excel数据: {len(excel_df)} 条记录")
    print(f"CSV数据: {len(csv_df)} 条记录")
    
    # 示例：处理前几条记录进行测试
    sample_excel = excel_df.head(3)
    sample_csv = csv_df[csv_df['dist'] == '未央区'].head(5)
    
    print("\n=== 示例数据 ===")
    print("Excel样本:")
    for i, row in sample_excel.iterrows():
        district = row['行政区']
        risk_point = row['风险点']
        print(f"{i+1}. {district} - {risk_point}")
    
    print("\nCSV样本:")
    for i, row in sample_csv.iterrows():
        district = row['dist']
        road_addr = row['road_address']
        poi_addr = row['poi_address']
        print(f"{i+1}. {district} - {road_addr} - {poi_addr}")
    
    # 准备数据
    excel_records = []
    for _, row in sample_excel.iterrows():
        excel_records.append({
            '行政区': row['行政区'],
            '风险点': row['风险点']
        })
    
    csv_records = []
    for _, row in sample_csv.iterrows():
        csv_records.append({
            'dist': row['dist'],
            'road_address': row['road_address'],
            'poi_address': row['poi_address'],
            'lon': row['lon'],
            'lat': row['lat']
        })
    
    # 生成prompt
    prompt = create_matching_prompt(excel_records, csv_records)
    
    print("\n" + "="*50)
    print("生成的Prompt:")
    print("="*50)
    print(prompt)
    
    print("\n" + "="*50)
    print("使用说明:")
    print("="*50)
    print("1. 将上述prompt发送给智谱AI GLM-4模型")
    print("2. 模型会返回JSON格式的匹配结果")
    print("3. 根据匹配结果将经纬度和地址信息添加到Excel文件中")
    print("4. 对于低置信度的匹配，建议人工确认")

if __name__ == "__main__":
    main()
