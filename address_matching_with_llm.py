import pandas as pd
import json
from typing import List, Dict, Tuple
import time

class AddressMatcherLLM:
    def __init__(self, model: str = "gpt-3.5-turbo"):
        """
        初始化地址匹配器

        Args:
            model: 使用的模型名称
        """
        self.model = model
        
    def create_matching_prompt(self, excel_records: List[Dict], csv_records: List[Dict]) -> str:
        """
        创建地址匹配的prompt
        
        Args:
            excel_records: Excel文件中的记录列表
            csv_records: CSV文件中的记录列表
            
        Returns:
            格式化的prompt字符串
        """
        
        prompt = """你是一个专业的地理位置匹配专家。请帮我判断以下两组地址数据中哪些是指向同一个地点的。

**任务说明：**
- 第一组数据来自积水点风险评估表，包含风险点描述
- 第二组数据来自地图服务，包含详细的道路地址和POI地址
- 请判断第一组中的每个风险点是否与第二组中的某个地点匹配

**匹配原则：**
1. 优先考虑地理位置的实际对应关系，而不是文字的完全一致
2. 考虑道路交叉口、下穿隧道、立交桥等交通设施的描述差异
3. 注意同一地点可能有不同的表达方式（如"下穿"vs"交叉口"）
4. 考虑行政区划的一致性
5. POI地址可以作为辅助判断依据

**第一组数据（风险点）：**
"""
        
        for i, record in enumerate(excel_records, 1):
            prompt += f"{i}. 行政区：{record['行政区']} | 风险点：{record['风险点']}\n"
        
        prompt += "\n**第二组数据（地图地址）：**\n"
        
        for i, record in enumerate(csv_records, 1):
            prompt += f"{i}. 行政区：{record['dist']} | 道路地址：{record['road_address']} | POI地址：{record['poi_address']} | 坐标：({record['lon']}, {record['lat']})\n"
        
        prompt += """
**请按以下JSON格式返回匹配结果：**
```json
{
    "matches": [
        {
            "excel_index": 1,
            "csv_index": 3,
            "confidence": "high",
            "reason": "两者都描述了同一个道路交叉口，只是表达方式略有不同"
        }
    ],
    "unmatched_excel": [2, 4],
    "unmatched_csv": [1, 5],
    "summary": {
        "total_matches": 1,
        "high_confidence": 1,
        "medium_confidence": 0,
        "low_confidence": 0
    }
}
```

**字段说明：**
- excel_index: 第一组数据的序号
- csv_index: 第二组数据的序号  
- confidence: 匹配置信度 ("high", "medium", "low")
- reason: 匹配理由
- unmatched_excel: 第一组中未匹配的记录序号
- unmatched_csv: 第二组中未匹配的记录序号

请仔细分析每个地点的特征，给出准确的匹配结果。"""

        return prompt
    
    def match_addresses_batch(self, excel_df: pd.DataFrame, csv_df: pd.DataFrame, 
                            batch_size: int = 5) -> List[Dict]:
        """
        批量匹配地址
        
        Args:
            excel_df: Excel数据框
            csv_df: CSV数据框
            batch_size: 每批处理的记录数量
            
        Returns:
            匹配结果列表
        """
        results = []
        
        # 按行政区分组处理
        for district in excel_df['行政区'].unique():
            print(f"正在处理行政区: {district}")
            
            excel_district = excel_df[excel_df['行政区'] == district]
            csv_district = csv_df[csv_df['dist'] == district]
            
            # 分批处理
            for i in range(0, len(excel_district), batch_size):
                excel_batch = excel_district.iloc[i:i+batch_size]
                
                # 准备数据
                excel_records = []
                for _, row in excel_batch.iterrows():
                    excel_records.append({
                        'original_index': row.name,
                        '行政区': row['行政区'],
                        '风险点': row['风险点'],
                        '编号': row['编号']
                    })
                
                csv_records = []
                for _, row in csv_district.iterrows():
                    csv_records.append({
                        'original_index': row.name,
                        'dist': row['dist'],
                        'road_address': row['road_address'],
                        'poi_address': row['poi_address'],
                        'lon': row['lon'],
                        'lat': row['lat']
                    })
                
                # 调用LLM进行匹配
                try:
                    batch_result = self.call_llm_for_matching(excel_records, csv_records)
                    batch_result['district'] = district
                    batch_result['batch_start_index'] = i
                    results.append(batch_result)
                    
                    # 避免API调用过于频繁
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"处理批次时出错: {e}")
                    continue
        
        return results
    
    def call_llm_for_matching(self, excel_records: List[Dict], csv_records: List[Dict]) -> Dict:
        """
        生成用于LLM匹配的prompt（演示版本）

        Args:
            excel_records: Excel记录列表
            csv_records: CSV记录列表

        Returns:
            包含prompt的字典
        """
        prompt = self.create_matching_prompt(excel_records, csv_records)

        return {
            "prompt": prompt,
            "excel_records": excel_records,
            "csv_records": csv_records,
            "note": "这是演示版本，实际使用时需要调用LLM API"
        }

def main():
    """
    主函数：演示如何使用地址匹配器
    """
    # 读取数据
    excel_df = pd.read_excel('ocr表格1h201.9mm.xlsx')
    csv_df = pd.read_csv('积水点_带地址.csv', encoding='gbk')
    
    print(f"Excel数据: {len(excel_df)} 条记录")
    print(f"CSV数据: {len(csv_df)} 条记录")
    
    # 初始化匹配器（需要设置OpenAI API密钥）
    # matcher = AddressMatcherLLM(api_key="your-openai-api-key")
    
    # 示例：处理前几条记录进行测试
    sample_excel = excel_df.head(3)
    sample_csv = csv_df[csv_df['dist'] == '未央区'].head(5)
    
    print("\n=== 示例数据 ===")
    print("Excel样本:")
    for i, row in sample_excel.iterrows():
        print(f"{i+1}. {row['行政区']} - {row['风险点']}")
    
    print("\nCSV样本:")
    for i, row in sample_csv.iterrows():
        print(f"{i+1}. {row['dist']} - {row['road_address']} - {row['poi_address']}")
    
    # 创建示例prompt
    matcher = AddressMatcherLLM()
    excel_records = []
    for _, row in sample_excel.iterrows():
        excel_records.append({
            '行政区': row['行政区'],
            '风险点': row['风险点']
        })
    
    csv_records = []
    for _, row in sample_csv.iterrows():
        csv_records.append({
            'dist': row['dist'],
            'road_address': row['road_address'],
            'poi_address': row['poi_address'],
            'lon': row['lon'],
            'lat': row['lat']
        })
    
    prompt = matcher.create_matching_prompt(excel_records, csv_records)
    
    print("\n=== 生成的Prompt ===")
    print(prompt)

if __name__ == "__main__":
    main()
