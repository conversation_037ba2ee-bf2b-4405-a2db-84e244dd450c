#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试逆地理编码功能
"""

import pandas as pd
from reverse_geocoding import AmapGeocoder

def test_single_point():
    """测试单个点的逆地理编码"""

    # 测试几个不同的点
    test_points = [
        (108.933768, 34.349163, "点1"),
        (108.928245, 34.348988, "点2"),
        (108.914918, 34.349339, "点3"),
        (108.943676, 34.369065, "点4"),
        (108.960685, 34.371827, "点5")
    ]

    print("=== 测试逆地理编码功能（道路交叉口版本）===")

    # 初始化地理编码器
    geocoder = AmapGeocoder("0b65cd528f00db9f2cb533a1b2fee65f")

    for lng, lat, name in test_points:
        print(f"\n--- 测试 {name}: ({lng}, {lat}) ---")

        # 获取两种地址信息（启用调试模式）
        result = geocoder.reverse_geocode(lng, lat, debug=True)
        road_addr, poi_addr = result

        print(f"🛣️  道路地址: {road_addr}")
        print(f"🏢 POI地址: {poi_addr}")
        print("-" * 60)

    return True

def test_multiple_points():
    """测试多个点"""
    
    # 读取CSV文件的前5个点进行测试
    try:
        df = pd.read_csv("1h201.9-积水点.csv")
        test_df = df.head(5)  # 只测试前5个点
        
        print("\n=== 测试前5个点 ===")
        
        geocoder = AmapGeocoder("0b65cd528f00db9f2cb533a1b2fee65f")
        
        for index, row in test_df.iterrows():
            lng = row['lon']
            lat = row['lat']
            point_id = row['id']
            
            print(f"\n--- 点 {point_id} ({lng}, {lat}) ---")
            result = geocoder.reverse_geocode(lng, lat, debug=False)
            road_addr, poi_addr = result
            print(f"🛣️  道路地址: {road_addr}")
            print(f"🏢 POI地址: {poi_addr}")
            
    except Exception as e:
        print(f"测试多个点时出错: {e}")

if __name__ == "__main__":
    # 先测试单个点
    test_single_point()
    
    # 询问是否继续测试多个点
    user_input = input("\n是否测试多个点？(y/n): ").lower().strip()
    if user_input == 'y':
        test_multiple_points()
