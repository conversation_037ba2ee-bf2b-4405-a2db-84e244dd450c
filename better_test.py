import pandas as pd
import json
import requests
import time
import config

def find_potential_matches():
    """寻找可能匹配的数据进行测试"""
    
    print("=== 寻找潜在匹配数据 ===")
    
    # 读取数据
    excel_df = pd.read_excel(config.EXCEL_FILE)
    csv_df = pd.read_csv(config.CSV_FILE, encoding='gbk')
    
    print(f"数据加载成功: Excel {len(excel_df)} 条, CSV {len(csv_df)} 条")
    
    # 分析数据中的关键词
    print(f"\n=== 分析Excel中的关键词 ===")
    excel_keywords = set()
    for point in excel_df['风险点']:
        if pd.notna(point):
            # 提取可能的道路名称
            words = str(point).replace('（', ' ').replace('）', ' ').replace('(', ' ').replace(')', ' ').split()
            for word in words:
                if len(word) > 2 and ('路' in word or '街' in word or '大道' in word or '立交' in word):
                    excel_keywords.add(word)
    
    print(f"Excel中的道路关键词: {list(excel_keywords)[:10]}...")
    
    print(f"\n=== 分析CSV中的关键词 ===")
    csv_keywords = set()
    for addr in csv_df['road_address']:
        if pd.notna(addr):
            words = str(addr).replace('与', ' ').replace('交叉口', ' ').split()
            for word in words:
                if len(word) > 2 and ('路' in word or '街' in word or '大道' in word):
                    csv_keywords.add(word)
    
    print(f"CSV中的道路关键词: {list(csv_keywords)[:10]}...")
    
    # 寻找共同关键词
    common_keywords = excel_keywords & csv_keywords
    print(f"\n=== 共同关键词 ===")
    print(f"共同关键词: {list(common_keywords)}")
    
    if common_keywords:
        # 选择一个共同关键词进行测试
        test_keyword = list(common_keywords)[0]
        print(f"\n=== 使用关键词 '{test_keyword}' 进行测试 ===")
        
        # 找到包含这个关键词的记录
        excel_matches = excel_df[excel_df['风险点'].str.contains(test_keyword, na=False)]
        csv_matches = csv_df[csv_df['road_address'].str.contains(test_keyword, na=False)]
        
        print(f"Excel中包含'{test_keyword}'的记录: {len(excel_matches)} 条")
        print(f"CSV中包含'{test_keyword}'的记录: {len(csv_matches)} 条")
        
        if len(excel_matches) > 0 and len(csv_matches) > 0:
            # 进行匹配测试
            test_matching_with_keyword(excel_matches.head(2), csv_matches.head(3), test_keyword)
        else:
            print("没有找到足够的匹配数据")
    else:
        print("没有找到共同关键词，使用默认测试")
        # 使用默认的测试数据
        test_default_matching()

def test_matching_with_keyword(excel_data, csv_data, keyword):
    """使用特定关键词进行匹配测试"""
    
    print(f"\n=== 使用关键词 '{keyword}' 进行匹配测试 ===")
    
    print("📋 Excel记录:")
    for i, row in excel_data.iterrows():
        print(f"  {i+1}: {row['行政区']} - {row['风险点']}")
    
    print("📍 CSV记录:")
    for i, row in csv_data.iterrows():
        print(f"  {i+1}: {row['dist']} - {row['road_address']} - {row['poi_address']}")
    
    # 构建匹配prompt
    excel_records = []
    for idx, row in excel_data.iterrows():
        excel_records.append({
            'index': len(excel_records) + 1,
            'district': row['行政区'],
            'point': row['风险点']
        })
    
    csv_records = []
    for idx, row in csv_data.iterrows():
        csv_records.append({
            'index': len(csv_records) + 1,
            'district': row['dist'],
            'road': row['road_address'],
            'poi': row['poi_address'],
            'lon': row['lon'],
            'lat': row['lat']
        })
    
    # 创建prompt
    prompt = f"""请判断以下积水点风险评估数据与地图地址数据的匹配关系。
注意：这些数据都包含关键词"{keyword}"，请重点关注地理位置的对应关系。

积水点数据：
"""
    
    for record in excel_records:
        prompt += f"{record['index']}. {record['district']} - {record['point']}\n"
    
    prompt += f"\n地图地址数据：\n"
    
    for record in csv_records:
        prompt += f"{record['index']}. {record['district']} - {record['road']} - {record['poi']}\n"
    
    prompt += f"""
匹配原则：
1. 优先考虑行政区一致性
2. 关注道路名称的对应关系
3. 理解"下穿"、"立交"、"交叉口"等可能指向同一地点
4. 考虑POI信息作为辅助判断

请返回JSON格式的匹配结果：
{{
    "matches": [
        {{"excel_index": 1, "csv_index": 2, "confidence": "high", "reason": "详细的匹配理由"}}
    ],
    "unmatched_excel": [2],
    "unmatched_csv": [1, 3]
}}

只返回JSON，不要其他文字。"""
    
    # 调用API
    call_matching_api(prompt, f"关键词_{keyword}")

def test_default_matching():
    """默认匹配测试"""
    
    print(f"\n=== 默认匹配测试 ===")
    
    # 读取数据
    excel_df = pd.read_excel(config.EXCEL_FILE)
    csv_df = pd.read_csv(config.CSV_FILE, encoding='gbk')
    
    # 选择一些可能有关联的数据
    # 尝试找到同一行政区的数据
    district = '雁塔区'  # 换一个行政区试试
    
    excel_test = excel_df[excel_df['行政区'] == district].head(2)
    csv_test = csv_df[csv_df['dist'] == district].head(4)
    
    if len(excel_test) == 0 or len(csv_test) == 0:
        district = '碑林区'
        excel_test = excel_df[excel_df['行政区'] == district].head(2)
        csv_test = csv_df[csv_df['dist'] == district].head(4)
    
    print(f"测试行政区: {district}")
    print("📋 Excel记录:")
    for i, row in excel_test.iterrows():
        print(f"  {i+1}: {row['风险点']}")
    
    print("📍 CSV记录:")
    for i, row in csv_test.iterrows():
        print(f"  {i+1}: {row['road_address']} - {row['poi_address']}")
    
    # 构建prompt
    excel_records = []
    for idx, row in excel_test.iterrows():
        excel_records.append({
            'index': len(excel_records) + 1,
            'point': row['风险点']
        })
    
    csv_records = []
    for idx, row in csv_test.iterrows():
        csv_records.append({
            'index': len(csv_records) + 1,
            'road': row['road_address'],
            'poi': row['poi_address']
        })
    
    prompt = f"""请判断以下{district}的积水点与地图地址的匹配关系：

积水点数据：
"""
    
    for record in excel_records:
        prompt += f"{record['index']}. {record['point']}\n"
    
    prompt += f"\n地图地址数据：\n"
    
    for record in csv_records:
        prompt += f"{record['index']}. {record['road']} - {record['poi']}\n"
    
    prompt += f"""
请返回JSON格式的匹配结果：
{{
    "matches": [
        {{"excel_index": 1, "csv_index": 2, "confidence": "high", "reason": "匹配理由"}}
    ],
    "unmatched_excel": [],
    "unmatched_csv": []
}}

只返回JSON，不要其他文字。"""
    
    call_matching_api(prompt, f"默认_{district}")

def call_matching_api(prompt, test_name):
    """调用匹配API"""
    
    print(f"\n=== 调用API ({test_name}) ===")
    print(f"Prompt长度: {len(prompt)} 字符")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.ZHIPU_API_KEY}"
    }
    
    data = {
        "model": "glm-4-flash",
        "messages": [
            {
                "role": "system",
                "content": "你是专业的地理位置匹配专家。请仔细分析地址信息，给出准确的匹配结果。严格按照JSON格式返回，不要添加任何其他文字。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 1000
    }
    
    try:
        print("🚀 正在调用API...")
        start_time = time.time()
        
        response = requests.post(
            config.BASE_URL,
            headers=headers,
            json=data,
            timeout=60
        )
        
        end_time = time.time()
        print(f"📡 响应时间: {end_time - start_time:.2f} 秒")
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result_data = response.json()
            content = result_data['choices'][0]['message']['content']
            
            print(f"✅ API调用成功!")
            print(f"📝 返回内容: {content}")
            
            # 解析JSON
            try:
                json_content = content.strip()
                if json_content.startswith('```json'):
                    json_content = json_content[7:]
                if json_content.endswith('```'):
                    json_content = json_content[:-3]
                json_content = json_content.strip()
                
                match_result = json.loads(json_content)
                
                print(f"\n=== 解析结果 ===")
                print(json.dumps(match_result, ensure_ascii=False, indent=2))
                
                # 保存结果
                output_file = f'测试结果_{test_name}.json'
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        'prompt': prompt,
                        'result': match_result,
                        'test_name': test_name
                    }, f, ensure_ascii=False, indent=2)
                
                print(f"💾 结果已保存到: {output_file}")
                
                return match_result
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return None
                
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return None

if __name__ == "__main__":
    find_potential_matches()
