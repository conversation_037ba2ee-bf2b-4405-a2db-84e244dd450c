import pandas as pd
import json
import requests
from typing import List, Dict
import time
import config

class AddressMatcherWithLLM:
    def __init__(self):
        self.api_key = config.ZHIPU_API_KEY
        self.model = config.MODEL_NAME
        self.base_url = config.BASE_URL
        self.timeout = config.TIMEOUT
        self.temperature = config.TEMPERATURE
        self.max_tokens = config.MAX_TOKENS

    def create_matching_prompt(self, excel_records: List[Dict], csv_records: List[Dict]) -> str:
        prompt = """你是一个专业的地理位置匹配专家。请帮我判断以下两组地址数据中哪些是指向同一个地点的。

**任务说明：**
- 第一组数据来自积水点风险评估表，包含风险点描述
- 第二组数据来自地图服务，包含详细的道路地址和POI地址
- 请判断第一组中的每个风险点是否与第二组中的某个地点匹配

**匹配原则：**
1. 优先考虑地理位置的实际对应关系，而不是文字的完全一致
2. 考虑道路交叉口、下穿隧道、立交桥等交通设施的描述差异
3. 注意同一地点可能有不同的表达方式（如"下穿"vs"交叉口"）
4. 考虑行政区划的一致性
5. POI地址可以作为辅助判断依据

**第一组数据（风险点）：**
"""
        
        for i, record in enumerate(excel_records, 1):
            prompt += f"{i}. 行政区：{record['行政区']} | 风险点：{record['风险点']}\n"
        
        prompt += "\n**第二组数据（地图地址）：**\n"
        
        for i, record in enumerate(csv_records, 1):
            prompt += f"{i}. 行政区：{record['dist']} | 道路地址：{record['road_address']} | POI地址：{record['poi_address']} | 坐标：({record['lon']}, {record['lat']})\n"
        
        prompt += """
**请按以下JSON格式返回匹配结果：**
```json
{
    "matches": [
        {
            "excel_index": 1,
            "csv_index": 3,
            "confidence": "high",
            "reason": "两者都描述了同一个道路交叉口，只是表达方式略有不同"
        }
    ],
    "unmatched_excel": [2],
    "unmatched_csv": [1, 2],
    "summary": {
        "total_matches": 1,
        "high_confidence": 1,
        "medium_confidence": 0,
        "low_confidence": 0
    }
}
```

请仔细分析每个地点的特征，给出准确的匹配结果。只返回JSON，不要其他说明文字。"""

        return prompt

    def call_llm_api(self, prompt: str, max_retries: int = 3) -> Dict:
        if not self.api_key or self.api_key == "your-api-key-here":
            return {"error": "请在config.py中设置有效的API密钥"}

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的地理位置匹配专家，擅长分析和匹配不同来源的地址数据。请严格按照要求的JSON格式返回结果，不要添加任何其他文字。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }

        print(f"🔧 调试信息:")
        print(f"   API地址: {self.base_url}")
        print(f"   模型: {self.model}")
        print(f"   超时设置: {self.timeout}秒")
        print(f"   API密钥前缀: {self.api_key[:20]}...")
        print(f"   Prompt长度: {len(prompt)} 字符")

        for attempt in range(max_retries):
            try:
                print(f"🚀 第 {attempt + 1} 次尝试调用智谱AI API...")

                # 测试网络连接
                print("🌐 测试网络连接...")
                test_response = requests.get("https://www.baidu.com", timeout=5)
                print(f"   网络连接正常 (状态码: {test_response.status_code})")

                # 调用API
                response = requests.post(
                    self.base_url,
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                )

                print(f"📡 API响应状态码: {response.status_code}")
                print(f"📊 响应头: {dict(response.headers)}")

                response.raise_for_status()

                result_data = response.json()
                print(f"✅ API调用成功，解析JSON...")

                if 'choices' not in result_data:
                    print(f"❌ 响应格式异常: {result_data}")
                    return {"error": "API响应格式异常", "raw_response": result_data}

                # 处理不同模型的响应格式
                message = result_data['choices'][0]['message']
                content = message.get('content', '')

                # 如果content为空，检查是否有reasoning_content（某些模型的特殊字段）
                if not content and 'reasoning_content' in message:
                    reasoning = message['reasoning_content']
                    print(f"📝 发现reasoning_content: {reasoning[:200]}...")
                    # 对于glm-4.5-flash等模型，可能需要特殊处理
                    return {"error": "模型返回了reasoning_content但content为空，请尝试其他模型或调整参数"}

                print(f"📝 API返回内容长度: {len(content)} 字符")
                print(f"📝 API返回内容预览: {content[:200]}...")

                # 尝试提取JSON部分
                json_start = content.find('```json')
                json_end = content.find('```', json_start + 7)

                if json_start != -1 and json_end != -1:
                    json_str = content[json_start + 7:json_end].strip()
                    print("🔍 找到JSON代码块，正在解析...")
                    result = json.loads(json_str)
                else:
                    # 如果没有找到代码块，尝试直接解析
                    print("🔍 未找到JSON代码块，尝试直接解析...")
                    content = content.strip()
                    if content.startswith('```json'):
                        content = content[7:]
                    if content.endswith('```'):
                        content = content[:-3]
                    result = json.loads(content.strip())

                print("✅ JSON解析成功!")
                return result

            except requests.exceptions.Timeout as e:
                print(f"⏰ 第 {attempt + 1} 次尝试超时: {e}")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    return {"error": f"API调用超时，已重试 {max_retries} 次", "details": str(e)}

            except requests.exceptions.RequestException as e:
                print(f"🌐 第 {attempt + 1} 次网络请求失败: {e}")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 3
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    return {"error": f"网络请求失败，已重试 {max_retries} 次", "details": str(e)}

            except json.JSONDecodeError as e:
                print(f"📄 JSON解析失败: {e}")
                print(f"📄 原始内容: {content if 'content' in locals() else 'None'}")
                return {"error": f"JSON解析失败: {str(e)}", "raw_response": content if 'content' in locals() else None}

            except Exception as e:
                print(f"❌ 未知错误: {e}")
                return {"error": f"未知错误: {str(e)}", "raw_response": content if 'content' in locals() else None}

        return {"error": f"所有重试都失败了，共尝试 {max_retries} 次"}

def run_demo():
    """运行演示"""
    
    print("=== 智谱AI地址匹配演示 ===")
    
    # 检查配置
    if config.ZHIPU_API_KEY == "your-api-key-here":
        print("❌ 请先在config.py中设置您的智谱AI API密钥")
        print("📝 获取API密钥: https://open.bigmodel.cn/")
        print("🔧 编辑config.py文件，将ZHIPU_API_KEY设置为您的实际密钥")
        return
    
    # 读取数据
    try:
        excel_df = pd.read_excel(config.EXCEL_FILE)
        csv_df = pd.read_csv(config.CSV_FILE, encoding='gbk')
        print(f"✅ 数据加载成功: Excel {len(excel_df)} 条, CSV {len(csv_df)} 条")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 初始化匹配器
    matcher = AddressMatcherWithLLM()
    
    # 选择测试数据
    district = '未央区'
    
    # 尝试找到包含相似地名的记录进行测试
    excel_test = excel_df[excel_df['行政区'] == district].head(2)
    csv_test = csv_df[csv_df['dist'] == district].head(5)
    
    print(f"\n=== 测试数据 ({district}) ===")
    print("📋 Excel记录:")
    for i, row in excel_test.iterrows():
        print(f"  {i+1}: {row['风险点']}")
    
    print("📍 CSV记录:")
    for i, row in csv_test.iterrows():
        print(f"  {i+1}: {row['road_address']} - {row['poi_address']}")
    
    # 准备数据
    excel_records = []
    for idx, row in excel_test.iterrows():
        excel_records.append({
            'original_index': idx,
            '行政区': row['行政区'],
            '风险点': row['风险点'],
            '编号': row.get('编号', '')
        })
    
    csv_records = []
    for idx, row in csv_test.iterrows():
        csv_records.append({
            'original_index': idx,
            'dist': row['dist'],
            'road_address': row['road_address'],
            'poi_address': row['poi_address'],
            'lon': row['lon'],
            'lat': row['lat']
        })
    
    # 调用API进行匹配
    prompt = matcher.create_matching_prompt(excel_records, csv_records)
    result = matcher.call_llm_api(prompt)
    
    print(f"\n=== 匹配结果 ===")
    if 'error' in result:
        print(f"❌ 错误: {result['error']}")
        if 'raw_response' in result:
            print(f"📄 原始响应: {result['raw_response']}")
        return
    
    print("✅ API调用成功!")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 显示匹配详情
    if 'matches' in result and result['matches']:
        print(f"\n=== 匹配详情 ===")
        for match in result['matches']:
            excel_idx = match['excel_index'] - 1
            csv_idx = match['csv_index'] - 1
            
            if excel_idx < len(excel_records) and csv_idx < len(csv_records):
                excel_record = excel_records[excel_idx]
                csv_record = csv_records[csv_idx]
                
                print(f"\n🎯 匹配 {match['excel_index']} -> {match['csv_index']}:")
                print(f"   📍 Excel: {excel_record['风险点']}")
                print(f"   🗺️  CSV: {csv_record['road_address']}")
                print(f"   🏢 POI: {csv_record['poi_address']}")
                print(f"   📍 坐标: ({csv_record['lon']}, {csv_record['lat']})")
                print(f"   🎯 置信度: {match['confidence']}")
                print(f"   💭 理由: {match['reason']}")
        
        # 统计信息
        if 'summary' in result:
            summary = result['summary']
            print(f"\n=== 统计信息 ===")
            print(f"📊 总匹配数: {summary.get('total_matches', 0)}")
            print(f"🟢 高置信度: {summary.get('high_confidence', 0)}")
            print(f"🟡 中置信度: {summary.get('medium_confidence', 0)}")
            print(f"🔴 低置信度: {summary.get('low_confidence', 0)}")
    else:
        print("❌ 没有找到匹配项")
    
    # 保存结果
    output_file = f'演示结果_{district}.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'excel_records': excel_records,
            'csv_records': csv_records,
            'match_result': result,
            'prompt': prompt
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到: {output_file}")
    
    print(f"\n=== 下一步操作 ===")
    print("1. 如果结果满意，可以扩展到处理更多数据")
    print("2. 调整config.py中的BATCH_SIZE来控制批次大小")
    print("3. 修改代码来处理所有行政区的数据")
    print("4. 根据置信度进行人工审核")

if __name__ == "__main__":
    run_demo()
