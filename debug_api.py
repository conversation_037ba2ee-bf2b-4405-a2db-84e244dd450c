import pandas as pd
import json
import requests
import time
import config

def debug_api_response():
    """详细调试API响应"""
    
    print("=== 智谱AI API详细调试 ===")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.ZHIPU_API_KEY}"
    }
    
    # 测试不同的模型
    models_to_test = ["glm-4", "glm-4-flash", "glm-4.5-flash"]
    
    for model in models_to_test:
        print(f"\n🧪 测试模型: {model}")
        
        data = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": "请说'你好'"
                }
            ],
            "temperature": 0.1,
            "max_tokens": 50
        }
        
        try:
            response = requests.post(
                config.BASE_URL,
                headers=headers,
                json=data,
                timeout=30
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    print(f"   内容: '{content}'")
                    print(f"   内容长度: {len(content)}")
                    print(f"   内容类型: {type(content)}")
                else:
                    print("   ❌ 响应中没有choices字段")
            else:
                print(f"   ❌ 请求失败: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")

def test_address_matching_detailed():
    """详细测试地址匹配"""
    
    print(f"\n=== 详细地址匹配测试 ===")
    
    # 读取数据
    excel_df = pd.read_excel(config.EXCEL_FILE)
    csv_df = pd.read_csv(config.CSV_FILE, encoding='gbk')
    
    # 选择测试数据
    district = '未央区'
    excel_sample = excel_df[excel_df['行政区'] == district].head(1)
    csv_sample = csv_df[csv_df['dist'] == district].head(2)
    
    excel_point = excel_sample.iloc[0]['风险点']
    csv_addr1 = csv_sample.iloc[0]['road_address']
    
    print(f"测试数据:")
    print(f"  Excel: {excel_point}")
    print(f"  CSV: {csv_addr1}")
    
    # 构建简单的prompt
    prompt = f"""请判断这两个地址是否指向同一地点：

地址1：{excel_point}
地址2：{csv_addr1}

请回答：是 或 否"""
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.ZHIPU_API_KEY}"
    }
    
    data = {
        "model": "glm-4",  # 使用基础模型
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 100
    }
    
    print(f"\n发送的请求:")
    print(json.dumps(data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(
            config.BASE_URL,
            headers=headers,
            json=data,
            timeout=60
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"\n完整响应:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"\n提取的内容: '{content}'")
                print(f"内容长度: {len(content)}")
                
                if not content or content.strip() == "":
                    print("⚠️ 内容为空！")
                    
                    # 检查是否有其他字段
                    choice = result['choices'][0]
                    print(f"完整choice对象: {choice}")
                    
        else:
            print(f"❌ 请求失败")
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        traceback.print_exc()

def test_json_format():
    """测试JSON格式返回"""
    
    print(f"\n=== 测试JSON格式返回 ===")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.ZHIPU_API_KEY}"
    }
    
    prompt = """请返回以下JSON格式的数据：
{"result": "测试成功", "number": 123}

只返回JSON，不要其他文字。"""
    
    data = {
        "model": "glm-4",
        "messages": [
            {
                "role": "system",
                "content": "你是一个JSON格式化助手，只返回有效的JSON数据。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 100
    }
    
    try:
        response = requests.post(
            config.BASE_URL,
            headers=headers,
            json=data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"返回内容: '{content}'")
            
            if content:
                try:
                    json_result = json.loads(content)
                    print(f"JSON解析成功: {json_result}")
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
            else:
                print("⚠️ 返回内容为空")
                
        else:
            print(f"请求失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"异常: {e}")

if __name__ == "__main__":
    debug_api_response()
    test_address_matching_detailed()
    test_json_format()
