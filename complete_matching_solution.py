import pandas as pd
import json
import requests
from typing import List, Dict
import time

class AddressMatcherWithLLM:
    def __init__(self, api_key: str = None, model: str = "glm-4"):
        """
        初始化地址匹配器
        
        Args:
            api_key: 智谱AI API密钥
            model: 使用的模型名称
        """
        self.api_key = api_key
        self.model = model
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"

    def create_matching_prompt(self, excel_records: List[Dict], csv_records: List[Dict]) -> str:
        """创建地址匹配的prompt"""
        
        prompt = """你是一个专业的地理位置匹配专家。请帮我判断以下两组地址数据中哪些是指向同一个地点的。

**任务说明：**
- 第一组数据来自积水点风险评估表，包含风险点描述
- 第二组数据来自地图服务，包含详细的道路地址和POI地址
- 请判断第一组中的每个风险点是否与第二组中的某个地点匹配

**匹配原则：**
1. 优先考虑地理位置的实际对应关系，而不是文字的完全一致
2. 考虑道路交叉口、下穿隧道、立交桥等交通设施的描述差异
3. 注意同一地点可能有不同的表达方式（如"下穿"vs"交叉口"）
4. 考虑行政区划的一致性
5. POI地址可以作为辅助判断依据

**第一组数据（风险点）：**
"""
        
        for i, record in enumerate(excel_records, 1):
            prompt += f"{i}. 行政区：{record['行政区']} | 风险点：{record['风险点']}\n"
        
        prompt += "\n**第二组数据（地图地址）：**\n"
        
        for i, record in enumerate(csv_records, 1):
            prompt += f"{i}. 行政区：{record['dist']} | 道路地址：{record['road_address']} | POI地址：{record['poi_address']} | 坐标：({record['lon']}, {record['lat']})\n"
        
        prompt += """
**请按以下JSON格式返回匹配结果：**
```json
{
    "matches": [
        {
            "excel_index": 1,
            "csv_index": 3,
            "confidence": "high",
            "reason": "两者都描述了同一个道路交叉口，只是表达方式略有不同"
        }
    ],
    "unmatched_excel": [2, 4],
    "unmatched_csv": [1, 5],
    "summary": {
        "total_matches": 1,
        "high_confidence": 1,
        "medium_confidence": 0,
        "low_confidence": 0
    }
}
```

**字段说明：**
- excel_index: 第一组数据的序号
- csv_index: 第二组数据的序号  
- confidence: 匹配置信度 ("high", "medium", "low")
- reason: 匹配理由
- unmatched_excel: 第一组中未匹配的记录序号
- unmatched_csv: 第二组中未匹配的记录序号

请仔细分析每个地点的特征，给出准确的匹配结果。"""

        return prompt

    def call_llm_api(self, prompt: str) -> Dict:
        """调用智谱AI API"""
        if not self.api_key:
            return {"error": "未提供API密钥"}
        
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的地理位置匹配专家，擅长分析和匹配不同来源的地址数据。请严格按照要求的JSON格式返回结果。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 4096
            }
            
            response = requests.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()
            
            result_data = response.json()
            content = result_data['choices'][0]['message']['content']
            
            # 尝试提取JSON部分
            json_start = content.find('```json')
            json_end = content.find('```', json_start + 7)
            
            if json_start != -1 and json_end != -1:
                json_str = content[json_start + 7:json_end].strip()
                result = json.loads(json_str)
            else:
                # 如果没有找到代码块，尝试直接解析
                result = json.loads(content)
            
            return result
            
        except Exception as e:
            return {"error": str(e), "raw_response": content if 'content' in locals() else None}

    def process_district_batch(self, excel_df: pd.DataFrame, csv_df: pd.DataFrame, 
                             district: str, batch_size: int = 5) -> List[Dict]:
        """处理单个行政区的数据"""
        
        excel_district = excel_df[excel_df['行政区'] == district].copy()
        csv_district = csv_df[csv_df['dist'] == district].copy()
        
        print(f"处理 {district}: Excel {len(excel_district)} 条, CSV {len(csv_district)} 条")
        
        results = []
        
        # 分批处理Excel数据
        for i in range(0, len(excel_district), batch_size):
            excel_batch = excel_district.iloc[i:i+batch_size]
            
            # 准备数据
            excel_records = []
            for idx, row in excel_batch.iterrows():
                excel_records.append({
                    'original_index': idx,
                    '行政区': row['行政区'],
                    '风险点': row['风险点'],
                    '编号': row['编号']
                })
            
            csv_records = []
            for idx, row in csv_district.iterrows():
                csv_records.append({
                    'original_index': idx,
                    'dist': row['dist'],
                    'road_address': row['road_address'],
                    'poi_address': row['poi_address'],
                    'lon': row['lon'],
                    'lat': row['lat']
                })
            
            # 生成prompt
            prompt = self.create_matching_prompt(excel_records, csv_records)
            
            # 调用LLM
            if self.api_key:
                llm_result = self.call_llm_api(prompt)
                time.sleep(1)  # 避免API调用过于频繁
            else:
                llm_result = {"error": "演示模式，未调用API", "prompt": prompt}
            
            batch_result = {
                'district': district,
                'batch_index': i // batch_size,
                'excel_records': excel_records,
                'csv_records': csv_records,
                'llm_result': llm_result
            }
            
            results.append(batch_result)
            
            print(f"  批次 {i//batch_size + 1} 完成")
        
        return results

    def merge_data_based_on_matches(self, excel_df: pd.DataFrame, csv_df: pd.DataFrame, 
                                  match_results: List[Dict]) -> pd.DataFrame:
        """根据匹配结果合并数据"""
        
        # 创建结果DataFrame
        result_df = excel_df.copy()
        
        # 添加新列
        result_df['经度'] = None
        result_df['纬度'] = None
        result_df['道路地址'] = None
        result_df['POI地址'] = None
        result_df['匹配置信度'] = None
        result_df['匹配理由'] = None
        
        # 处理匹配结果
        for batch_result in match_results:
            if 'error' in batch_result['llm_result']:
                print(f"跳过错误批次: {batch_result['llm_result']['error']}")
                continue
            
            llm_result = batch_result['llm_result']
            excel_records = batch_result['excel_records']
            csv_records = batch_result['csv_records']
            
            if 'matches' not in llm_result:
                continue
            
            # 处理每个匹配
            for match in llm_result['matches']:
                excel_idx = match['excel_index'] - 1  # 转换为0基索引
                csv_idx = match['csv_index'] - 1
                
                if excel_idx < len(excel_records) and csv_idx < len(csv_records):
                    excel_original_idx = excel_records[excel_idx]['original_index']
                    csv_record = csv_records[csv_idx]
                    
                    # 更新结果DataFrame
                    result_df.loc[excel_original_idx, '经度'] = csv_record['lon']
                    result_df.loc[excel_original_idx, '纬度'] = csv_record['lat']
                    result_df.loc[excel_original_idx, '道路地址'] = csv_record['road_address']
                    result_df.loc[excel_original_idx, 'POI地址'] = csv_record['poi_address']
                    result_df.loc[excel_original_idx, '匹配置信度'] = match['confidence']
                    result_df.loc[excel_original_idx, '匹配理由'] = match['reason']
        
        return result_df

def main():
    """主函数演示"""
    
    # 读取数据
    excel_df = pd.read_excel('ocr表格1h201.9mm.xlsx')
    csv_df = pd.read_csv('积水点_带地址.csv', encoding='gbk')
    
    print(f"Excel数据: {len(excel_df)} 条记录")
    print(f"CSV数据: {len(csv_df)} 条记录")
    
    # 初始化匹配器（演示模式，不提供API密钥）
    matcher = AddressMatcherWithLLM()
    
    # 演示：处理未央区的一小批数据
    district = '未央区'
    excel_sample = excel_df[excel_df['行政区'] == district].head(2)
    csv_sample = csv_df[csv_df['dist'] == district].head(3)
    
    print(f"\n=== 演示处理 {district} ===")
    
    # 准备数据
    excel_records = []
    for idx, row in excel_sample.iterrows():
        excel_records.append({
            'original_index': idx,
            '行政区': row['行政区'],
            '风险点': row['风险点'],
            '编号': row['编号']
        })
    
    csv_records = []
    for idx, row in csv_sample.iterrows():
        csv_records.append({
            'original_index': idx,
            'dist': row['dist'],
            'road_address': row['road_address'],
            'poi_address': row['poi_address'],
            'lon': row['lon'],
            'lat': row['lat']
        })
    
    # 生成prompt
    prompt = matcher.create_matching_prompt(excel_records, csv_records)
    
    print("生成的Prompt:")
    print("-" * 50)
    print(prompt)
    
    print("\n" + "="*80)
    print("完整处理流程:")
    print("="*80)
    print("1. 设置API密钥: matcher = AddressMatcherWithLLM(api_key='your-api-key')")
    print("2. 按行政区处理: results = matcher.process_district_batch(excel_df, csv_df, '未央区')")
    print("3. 合并数据: final_df = matcher.merge_data_based_on_matches(excel_df, csv_df, results)")
    print("4. 保存结果: final_df.to_excel('合并结果.xlsx', index=False)")

if __name__ == "__main__":
    main()
