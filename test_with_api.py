import pandas as pd
import json
import requests
from typing import List, Dict
import time

class AddressMatcherWithLLM:
    def __init__(self, api_key: str = None, model: str = "glm-4"):
        self.api_key = api_key
        self.model = model
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"

    def create_matching_prompt(self, excel_records: List[Dict], csv_records: List[Dict]) -> str:
        prompt = """你是一个专业的地理位置匹配专家。请帮我判断以下两组地址数据中哪些是指向同一个地点的。

**任务说明：**
- 第一组数据来自积水点风险评估表，包含风险点描述
- 第二组数据来自地图服务，包含详细的道路地址和POI地址
- 请判断第一组中的每个风险点是否与第二组中的某个地点匹配

**匹配原则：**
1. 优先考虑地理位置的实际对应关系，而不是文字的完全一致
2. 考虑道路交叉口、下穿隧道、立交桥等交通设施的描述差异
3. 注意同一地点可能有不同的表达方式（如"下穿"vs"交叉口"）
4. 考虑行政区划的一致性
5. POI地址可以作为辅助判断依据

**第一组数据（风险点）：**
"""
        
        for i, record in enumerate(excel_records, 1):
            prompt += f"{i}. 行政区：{record['行政区']} | 风险点：{record['风险点']}\n"
        
        prompt += "\n**第二组数据（地图地址）：**\n"
        
        for i, record in enumerate(csv_records, 1):
            prompt += f"{i}. 行政区：{record['dist']} | 道路地址：{record['road_address']} | POI地址：{record['poi_address']} | 坐标：({record['lon']}, {record['lat']})\n"
        
        prompt += """
**请按以下JSON格式返回匹配结果：**
```json
{
    "matches": [
        {
            "excel_index": 1,
            "csv_index": 3,
            "confidence": "high",
            "reason": "两者都描述了同一个道路交叉口，只是表达方式略有不同"
        }
    ],
    "unmatched_excel": [2],
    "unmatched_csv": [1, 2],
    "summary": {
        "total_matches": 1,
        "high_confidence": 1,
        "medium_confidence": 0,
        "low_confidence": 0
    }
}
```

请仔细分析每个地点的特征，给出准确的匹配结果。只返回JSON，不要其他说明文字。"""

        return prompt

    def call_llm_api(self, prompt: str) -> Dict:
        if not self.api_key:
            return {"error": "未提供API密钥"}
        
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的地理位置匹配专家，擅长分析和匹配不同来源的地址数据。请严格按照要求的JSON格式返回结果，不要添加任何其他文字。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 4096
            }
            
            print("正在调用智谱AI API...")
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result_data = response.json()
            content = result_data['choices'][0]['message']['content']
            print(f"API返回内容: {content}")
            
            # 尝试提取JSON部分
            json_start = content.find('```json')
            json_end = content.find('```', json_start + 7)
            
            if json_start != -1 and json_end != -1:
                json_str = content[json_start + 7:json_end].strip()
                result = json.loads(json_str)
            else:
                # 如果没有找到代码块，尝试直接解析
                content = content.strip()
                if content.startswith('```json'):
                    content = content[7:]
                if content.endswith('```'):
                    content = content[:-3]
                result = json.loads(content.strip())
            
            print("API调用成功，JSON解析完成")
            return result
            
        except Exception as e:
            print(f"API调用失败: {e}")
            return {"error": str(e), "raw_response": content if 'content' in locals() else None}

def test_matching():
    """测试匹配功能"""
    
    # 在这里设置您的API密钥
    API_KEY = "your-api-key-here"  # 请替换为您的实际API密钥
    
    if API_KEY == "your-api-key-here":
        print("请先在代码中设置您的智谱AI API密钥")
        print("获取API密钥: https://open.bigmodel.cn/")
        return
    
    # 读取数据
    try:
        excel_df = pd.read_excel('ocr表格1h201.9mm.xlsx')
        csv_df = pd.read_csv('积水点_带地址.csv', encoding='gbk')
        print(f"数据加载成功: Excel {len(excel_df)} 条, CSV {len(csv_df)} 条")
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 初始化匹配器
    matcher = AddressMatcherWithLLM(api_key=API_KEY)
    
    # 选择测试数据 - 找一些可能匹配的记录
    district = '未央区'
    
    # 从Excel中选择包含"元朔大道"的记录
    excel_test = excel_df[
        (excel_df['行政区'] == district) & 
        (excel_df['风险点'].str.contains('元朔大道', na=False))
    ].head(2)
    
    # 从CSV中选择包含"元朔大道"的记录
    csv_test = csv_df[
        (csv_df['dist'] == district) & 
        (csv_df['road_address'].str.contains('元朔大道', na=False))
    ].head(3)
    
    # 如果没找到匹配的，就用前几条
    if len(excel_test) == 0:
        excel_test = excel_df[excel_df['行政区'] == district].head(2)
    if len(csv_test) == 0:
        csv_test = csv_df[csv_df['dist'] == district].head(3)
    
    print(f"\n=== 测试数据 ({district}) ===")
    print("Excel记录:")
    for i, row in excel_test.iterrows():
        print(f"  {i}: {row['风险点']}")
    
    print("CSV记录:")
    for i, row in csv_test.iterrows():
        print(f"  {i}: {row['road_address']} - {row['poi_address']}")
    
    # 准备数据
    excel_records = []
    for idx, row in excel_test.iterrows():
        excel_records.append({
            'original_index': idx,
            '行政区': row['行政区'],
            '风险点': row['风险点'],
            '编号': row.get('编号', '')
        })
    
    csv_records = []
    for idx, row in csv_test.iterrows():
        csv_records.append({
            'original_index': idx,
            'dist': row['dist'],
            'road_address': row['road_address'],
            'poi_address': row['poi_address'],
            'lon': row['lon'],
            'lat': row['lat']
        })
    
    # 生成prompt
    prompt = matcher.create_matching_prompt(excel_records, csv_records)
    print(f"\n=== 生成的Prompt ===")
    print(prompt[:500] + "..." if len(prompt) > 500 else prompt)
    
    # 调用API
    result = matcher.call_llm_api(prompt)
    
    print(f"\n=== 匹配结果 ===")
    if 'error' in result:
        print(f"错误: {result['error']}")
        if 'raw_response' in result:
            print(f"原始响应: {result['raw_response']}")
        return
    
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 显示匹配详情
    if 'matches' in result and result['matches']:
        print(f"\n=== 匹配详情 ===")
        for match in result['matches']:
            excel_idx = match['excel_index'] - 1
            csv_idx = match['csv_index'] - 1
            
            if excel_idx < len(excel_records) and csv_idx < len(csv_records):
                excel_record = excel_records[excel_idx]
                csv_record = csv_records[csv_idx]
                
                print(f"\n匹配 {match['excel_index']} -> {match['csv_index']}:")
                print(f"  Excel: {excel_record['风险点']}")
                print(f"  CSV: {csv_record['road_address']}")
                print(f"  POI: {csv_record['poi_address']}")
                print(f"  坐标: ({csv_record['lon']}, {csv_record['lat']})")
                print(f"  置信度: {match['confidence']}")
                print(f"  理由: {match['reason']}")
    else:
        print("没有找到匹配项")
    
    # 保存结果到文件
    output_file = f'测试匹配结果_{district}.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'excel_records': excel_records,
            'csv_records': csv_records,
            'match_result': result
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细结果已保存到: {output_file}")

if __name__ == "__main__":
    test_matching()
