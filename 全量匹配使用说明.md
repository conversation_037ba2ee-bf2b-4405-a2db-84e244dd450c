# 智谱AI全量地址匹配使用说明

## 🚀 快速开始

### 1. 设置API密钥
编辑 `config.py` 文件：
```python
ZHIPU_API_KEY = "your-actual-api-key-here"
```

### 2. 预览测试（推荐）
```bash
python preview_matching.py
```
- 处理少量数据测试效果
- 预估全量处理成本和时间
- 验证匹配质量

### 3. 全量处理
```bash
python full_matching_solution.py
```
- 处理所有数据
- 生成完整结果文件

## 📁 文件说明

### 核心文件
- **`full_matching_solution.py`** - 全量匹配主程序 ⭐
- **`preview_matching.py`** - 预览测试程序 ⭐
- **`config.py`** - 配置文件

### 测试文件
- `working_demo.py` - 基础工作演示
- `better_test.py` - 改进测试
- `debug_api.py` - API调试工具

## ⚙️ 配置参数

在 `config.py` 中可以调整：

```python
# 处理配置
BATCH_SIZE = 3              # 每批处理的Excel记录数
CSV_CANDIDATES = 5          # 每批CSV候选记录数
DELAY_BETWEEN_CALLS = 1     # API调用间隔（秒）
MAX_DISTRICTS = None        # 限制处理的行政区数量
ENABLE_PREVIEW = False      # 是否启用预览模式

# API配置
MODEL_NAME = "glm-4-flash"  # 使用的模型
TIMEOUT = 60                # 超时时间（秒）
```

## 💰 成本估算

### 预览模式
- 处理 6 条Excel记录
- 约 2-3 次API调用
- 成本约 ¥0.5-0.75

### 全量处理
- 处理 298 条Excel记录
- 约 100 次API调用
- 预估成本 ¥25-30
- 预估耗时 5-10 分钟

## 📊 输出文件

### Excel结果文件
`完整匹配结果_YYYYMMDD_HHMMSS.xlsx`

包含原始数据 + 新增列：
- 经度
- 纬度  
- 道路地址
- POI地址
- 匹配置信度
- 匹配理由
- 匹配状态

### JSON详情文件
`匹配详情_YYYYMMDD_HHMMSS.json`

包含：
- 所有匹配详情
- 统计信息
- 处理日志

## 🎯 匹配原理

### 智能匹配策略
1. **行政区筛选** - 优先匹配同一行政区的数据
2. **语义理解** - 理解"下穿"、"立交"、"交叉口"等不同表达
3. **地理关联** - 识别道路名称、地标的对应关系
4. **置信度评估** - 提供high/medium/low三级置信度

### 批处理机制
- 每批处理3条Excel记录
- 与5条CSV记录进行匹配
- 避免单次处理过多数据影响准确性

## 📈 质量控制

### 置信度分级
- **High**: 非常确信是同一地点，可直接使用
- **Medium**: 可能是同一地点，建议人工确认
- **Low**: 不太确定，需要人工审核

### 建议流程
1. 运行预览测试，检查匹配质量
2. 调整配置参数（如有需要）
3. 运行全量处理
4. 对Medium/Low置信度结果进行人工审核
5. 根据需要进行二次处理

## 🔧 故障排除

### 常见问题

**API调用失败**
- 检查API密钥是否正确
- 确认账户余额充足
- 检查网络连接

**匹配率过低**
- 调整BATCH_SIZE（减小批次）
- 增加CSV_CANDIDATES（更多候选）
- 检查数据质量

**处理速度慢**
- 减少DELAY_BETWEEN_CALLS
- 增加BATCH_SIZE（注意准确性）
- 使用更快的网络连接

### 错误代码
- `1113`: 余额不足，需要充值
- `429`: 请求过于频繁，会自动重试
- `超时`: 网络问题，会自动重试

## 📞 技术支持

### 日志信息
程序会显示详细的处理日志：
- 数据加载状态
- 批次处理进度
- API调用结果
- 匹配统计信息

### 调试模式
使用 `debug_api.py` 进行API连接测试：
```bash
python debug_api.py
```

## 🎉 使用建议

1. **首次使用**: 先运行预览模式熟悉流程
2. **成本控制**: 根据预览结果调整参数
3. **质量优先**: 宁可多次小批处理，确保准确性
4. **人工审核**: 对结果进行适当的人工验证
5. **备份数据**: 处理前备份原始数据

## 📋 检查清单

运行前确认：
- [ ] API密钥已设置
- [ ] 数据文件存在
- [ ] 网络连接正常
- [ ] 账户余额充足
- [ ] 配置参数合理

运行后检查：
- [ ] 匹配率是否合理
- [ ] 置信度分布是否正常
- [ ] 结果文件是否生成
- [ ] 统计信息是否正确
