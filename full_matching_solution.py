import pandas as pd
import json
import requests
import time
import config
from typing import List, Dict, Tuple
import os
from datetime import datetime

class FullAddressMatcherLLM:
    def __init__(self):
        self.api_key = config.ZHIPU_API_KEY
        self.model = "glm-4-flash"  # 使用测试成功的模型
        self.base_url = config.BASE_URL
        self.timeout = 60
        self.temperature = 0.1
        self.max_tokens = 2000
        self.batch_size = 3  # 每批处理的Excel记录数
        self.max_csv_per_batch = 20  # 每批最多处理的CSV记录数（避免prompt过长）
        
        # 统计信息
        self.stats = {
            'total_excel': 0,
            'total_csv': 0,
            'processed_excel': 0,
            'total_matches': 0,
            'high_confidence': 0,
            'medium_confidence': 0,
            'low_confidence': 0,
            'api_calls': 0,
            'api_errors': 0,
            'start_time': None,
            'districts_processed': []
        }

    def create_matching_prompt(self, excel_records: List[Dict], csv_records: List[Dict], district: str) -> str:
        """创建匹配prompt"""
        
        prompt = f"""你是专业的地理位置匹配专家。请判断以下{district}的积水点风险评估数据与地图地址数据的匹配关系。

**匹配原则：**
1. 优先考虑行政区一致性（都是{district}）
2. 关注道路名称、交叉口、立交桥等地理标识
3. 理解"下穿"、"立交"、"交叉口"等可能指向同一地点的不同表达
4. 考虑POI信息作为辅助判断依据
5. 只有高度确信是同一地点时才匹配

**积水点风险数据：**
"""
        
        for i, record in enumerate(excel_records, 1):
            prompt += f"{i}. {record['point']}\n"
        
        prompt += f"\n**地图地址数据：**\n"
        
        for i, record in enumerate(csv_records, 1):
            prompt += f"{i}. {record['road']} - {record['poi']} - 坐标({record['lon']}, {record['lat']})\n"
        
        prompt += f"""
**请返回JSON格式结果：**
```json
{{
    "matches": [
        {{"excel_index": 1, "csv_index": 3, "confidence": "high", "reason": "详细匹配理由"}}
    ],
    "unmatched_excel": [2],
    "unmatched_csv": [1, 2, 4, 5]
}}
```

confidence取值：
- "high": 非常确信是同一地点
- "medium": 可能是同一地点，需要人工确认
- "low": 不太确定，建议人工审核

只返回JSON，不要其他文字。"""

        return prompt

    def call_llm_api(self, prompt: str) -> Dict:
        """调用LLM API"""
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "你是专业的地理位置匹配专家。请仔细分析地址信息，给出准确的匹配结果。严格按照JSON格式返回，不要添加任何其他文字。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.post(
                    self.base_url,
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                )
                
                self.stats['api_calls'] += 1
                
                if response.status_code == 200:
                    result_data = response.json()
                    content = result_data['choices'][0]['message']['content']
                    
                    # 解析JSON
                    json_content = content.strip()
                    if json_content.startswith('```json'):
                        json_content = json_content[7:]
                    if json_content.endswith('```'):
                        json_content = json_content[:-3]
                    json_content = json_content.strip()
                    
                    result = json.loads(json_content)
                    return result
                    
                elif response.status_code == 429:
                    print(f"⏰ API限流，等待 {(attempt + 1) * 5} 秒后重试...")
                    time.sleep((attempt + 1) * 5)
                    continue
                else:
                    print(f"❌ API调用失败: {response.status_code} - {response.text}")
                    self.stats['api_errors'] += 1
                    return {"error": f"API调用失败: {response.status_code}"}
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始内容: {content if 'content' in locals() else 'None'}")
                self.stats['api_errors'] += 1
                return {"error": f"JSON解析失败: {str(e)}"}
                
            except Exception as e:
                print(f"❌ API调用异常: {e}")
                if attempt < max_retries - 1:
                    print(f"⏳ 等待 {(attempt + 1) * 3} 秒后重试...")
                    time.sleep((attempt + 1) * 3)
                else:
                    self.stats['api_errors'] += 1
                    return {"error": str(e)}
        
        return {"error": "所有重试都失败了"}

    def process_district_batch(self, excel_df: pd.DataFrame, csv_df: pd.DataFrame, district: str) -> List[Dict]:
        """处理单个行政区的数据"""
        
        print(f"\n=== 处理 {district} ===")
        
        # 筛选该行政区的数据
        excel_district = excel_df[excel_df['行政区'] == district].reset_index()
        csv_district = csv_df[csv_df['dist'] == district].reset_index()
        
        print(f"📊 {district}: Excel {len(excel_district)} 条, CSV {len(csv_district)} 条")
        
        if len(excel_district) == 0 or len(csv_district) == 0:
            print(f"⚠️ {district} 数据不足，跳过")
            return []
        
        all_matches = []
        
        # 分批处理Excel数据
        for i in range(0, len(excel_district), self.batch_size):
            batch_excel = excel_district.iloc[i:i+self.batch_size]
            
            print(f"🔄 处理批次 {i//self.batch_size + 1}/{(len(excel_district)-1)//self.batch_size + 1}")
            
            # 准备Excel记录
            excel_records = []
            for idx, row in batch_excel.iterrows():
                excel_records.append({
                    'original_index': row['index'],  # 原始DataFrame索引
                    'batch_index': len(excel_records) + 1,
                    'point': row['风险点'],
                    'district': row['行政区']
                })
            
            # 准备该行政区的所有CSV记录（限制数量避免prompt过长）
            csv_records = []
            csv_to_process = csv_district.head(self.max_csv_per_batch) if len(csv_district) > self.max_csv_per_batch else csv_district

            for idx, row in csv_to_process.iterrows():
                csv_records.append({
                    'original_index': row['index'],  # 原始DataFrame索引
                    'batch_index': len(csv_records) + 1,
                    'road': row['road_address'],
                    'poi': row['poi_address'],
                    'lon': row['lon'],
                    'lat': row['lat'],
                    'district': row['dist']
                })
            
            # 显示当前批次数据
            print("📋 Excel记录:")
            for record in excel_records:
                print(f"  {record['batch_index']}: {record['point']}")
            
            print("📍 CSV候选:")
            for record in csv_records[:3]:  # 只显示前3条
                print(f"  {record['batch_index']}: {record['road']}")
            
            # 创建prompt并调用API
            prompt = self.create_matching_prompt(excel_records, csv_records, district)
            result = self.call_llm_api(prompt)
            
            if 'error' in result:
                print(f"❌ 批次处理失败: {result['error']}")
                continue
            
            # 处理匹配结果
            batch_matches = self.process_batch_result(result, excel_records, csv_records)
            all_matches.extend(batch_matches)
            
            # 更新统计
            self.stats['processed_excel'] += len(excel_records)
            
            # API调用间隔
            time.sleep(1)
        
        self.stats['districts_processed'].append(district)
        return all_matches

    def process_batch_result(self, result: Dict, excel_records: List[Dict], csv_records: List[Dict]) -> List[Dict]:
        """处理批次匹配结果"""
        
        batch_matches = []
        
        if 'matches' in result:
            for match in result['matches']:
                excel_idx = match['excel_index'] - 1
                csv_idx = match['csv_index'] - 1
                
                if excel_idx < len(excel_records) and csv_idx < len(csv_records):
                    excel_record = excel_records[excel_idx]
                    csv_record = csv_records[csv_idx]
                    
                    match_info = {
                        'excel_original_index': excel_record['original_index'],
                        'csv_original_index': csv_record['original_index'],
                        'excel_point': excel_record['point'],
                        'csv_road': csv_record['road'],
                        'csv_poi': csv_record['poi'],
                        'csv_lon': csv_record['lon'],
                        'csv_lat': csv_record['lat'],
                        'confidence': match['confidence'],
                        'reason': match['reason'],
                        'district': excel_record['district']
                    }
                    
                    batch_matches.append(match_info)
                    
                    # 更新统计
                    self.stats['total_matches'] += 1
                    if match['confidence'] == 'high':
                        self.stats['high_confidence'] += 1
                    elif match['confidence'] == 'medium':
                        self.stats['medium_confidence'] += 1
                    else:
                        self.stats['low_confidence'] += 1
                    
                    print(f"✅ 匹配: {excel_record['point']} -> {csv_record['road']} ({match['confidence']})")
        
        return batch_matches

    def run_full_matching(self) -> Tuple[pd.DataFrame, Dict]:
        """运行完整匹配流程"""
        
        print("=== 智谱AI全量地址匹配 ===")
        self.stats['start_time'] = datetime.now()
        
        # 读取数据
        try:
            excel_df = pd.read_excel(config.EXCEL_FILE)
            csv_df = pd.read_csv(config.CSV_FILE, encoding='gbk')
            
            self.stats['total_excel'] = len(excel_df)
            self.stats['total_csv'] = len(csv_df)
            
            print(f"✅ 数据加载成功: Excel {len(excel_df)} 条, CSV {len(csv_df)} 条")
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None, self.stats
        
        # 获取所有行政区
        excel_districts = set(excel_df['行政区'].dropna())
        csv_districts = set(csv_df['dist'].dropna())
        common_districts = excel_districts & csv_districts
        
        print(f"📍 共同行政区: {list(common_districts)}")
        
        # 处理每个行政区
        all_matches = []
        for district in sorted(common_districts):
            district_matches = self.process_district_batch(excel_df, csv_df, district)
            all_matches.extend(district_matches)
        
        # 创建结果DataFrame
        result_df = self.create_result_dataframe(excel_df, all_matches)
        
        # 显示最终统计
        self.print_final_stats()
        
        return result_df, all_matches

    def create_result_dataframe(self, excel_df: pd.DataFrame, matches: List[Dict]) -> pd.DataFrame:
        """创建结果DataFrame"""
        
        print(f"\n=== 创建结果文件 ===")
        
        # 复制原始Excel数据
        result_df = excel_df.copy()
        
        # 添加新列
        result_df['经度'] = None
        result_df['纬度'] = None
        result_df['道路地址'] = None
        result_df['POI地址'] = None
        result_df['匹配置信度'] = None
        result_df['匹配理由'] = None
        result_df['匹配状态'] = '未匹配'
        
        # 应用匹配结果
        for match in matches:
            idx = match['excel_original_index']
            result_df.loc[idx, '经度'] = match['csv_lon']
            result_df.loc[idx, '纬度'] = match['csv_lat']
            result_df.loc[idx, '道路地址'] = match['csv_road']
            result_df.loc[idx, 'POI地址'] = match['csv_poi']
            result_df.loc[idx, '匹配置信度'] = match['confidence']
            result_df.loc[idx, '匹配理由'] = match['reason']
            result_df.loc[idx, '匹配状态'] = '已匹配'
        
        return result_df

    def print_final_stats(self):
        """打印最终统计信息"""
        
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        print(f"\n=== 匹配完成统计 ===")
        print(f"⏱️  总耗时: {duration}")
        print(f"📊 Excel总数: {self.stats['total_excel']}")
        print(f"📊 CSV总数: {self.stats['total_csv']}")
        print(f"🔄 已处理: {self.stats['processed_excel']}")
        print(f"✅ 总匹配数: {self.stats['total_matches']}")
        print(f"🟢 高置信度: {self.stats['high_confidence']}")
        print(f"🟡 中置信度: {self.stats['medium_confidence']}")
        print(f"🔴 低置信度: {self.stats['low_confidence']}")
        print(f"📡 API调用: {self.stats['api_calls']}")
        print(f"❌ API错误: {self.stats['api_errors']}")
        print(f"🏛️  已处理行政区: {', '.join(self.stats['districts_processed'])}")
        
        # 计算匹配率
        if self.stats['processed_excel'] > 0:
            match_rate = (self.stats['total_matches'] / self.stats['processed_excel']) * 100
            print(f"📈 匹配率: {match_rate:.1f}%")

def main():
    """主函数"""
    
    # 检查配置
    if config.ZHIPU_API_KEY == "your-api-key-here":
        print("❌ 请先在config.py中设置API密钥")
        return
    
    # 创建匹配器
    matcher = FullAddressMatcherLLM()
    
    # 运行匹配
    result_df, matches = matcher.run_full_matching()
    
    if result_df is not None:
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存Excel结果
        excel_output = f'完整匹配结果_{timestamp}.xlsx'
        result_df.to_excel(excel_output, index=False)
        print(f"📊 Excel结果已保存: {excel_output}")
        
        # 保存JSON详细结果
        json_output = f'匹配详情_{timestamp}.json'
        with open(json_output, 'w', encoding='utf-8') as f:
            json.dump({
                'matches': matches,
                'stats': matcher.stats,
                'timestamp': timestamp
            }, f, ensure_ascii=False, indent=2)
        print(f"📄 详细结果已保存: {json_output}")
        
        print(f"\n🎉 全量匹配完成！")
        print(f"📁 结果文件: {excel_output}")
        print(f"📁 详情文件: {json_output}")

if __name__ == "__main__":
    main()
