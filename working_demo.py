import pandas as pd
import json
import requests
import time
import config

def working_address_matching():
    """使用glm-4-flash模型的工作演示"""
    
    print("=== 智谱AI地址匹配工作演示 ===")
    
    # 读取数据
    try:
        excel_df = pd.read_excel(config.EXCEL_FILE)
        csv_df = pd.read_csv(config.CSV_FILE, encoding='gbk')
        print(f"✅ 数据加载成功: Excel {len(excel_df)} 条, CSV {len(csv_df)} 条")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 选择测试数据
    district = '未央区'
    excel_test = excel_df[excel_df['行政区'] == district].head(2)
    csv_test = csv_df[csv_df['dist'] == district].head(3)
    
    print(f"\n=== 测试数据 ({district}) ===")
    print("📋 Excel记录:")
    for i, row in excel_test.iterrows():
        print(f"  {i+1}: {row['风险点']}")
    
    print("📍 CSV记录:")
    for i, row in csv_test.iterrows():
        print(f"  {i+1}: {row['road_address']} - {row['poi_address']}")
    
    # 构建简化的匹配prompt
    excel_records = []
    for idx, row in excel_test.iterrows():
        excel_records.append({
            'index': len(excel_records) + 1,
            'point': row['风险点']
        })
    
    csv_records = []
    for idx, row in csv_test.iterrows():
        csv_records.append({
            'index': len(csv_records) + 1,
            'road': row['road_address'],
            'poi': row['poi_address'],
            'lon': row['lon'],
            'lat': row['lat']
        })
    
    # 创建简化的prompt
    prompt = f"""请判断以下积水点风险评估数据与地图地址数据的匹配关系：

积水点数据：
"""
    
    for record in excel_records:
        prompt += f"{record['index']}. {record['point']}\n"
    
    prompt += f"\n地图地址数据：\n"
    
    for record in csv_records:
        prompt += f"{record['index']}. {record['road']} - {record['poi']}\n"
    
    prompt += f"""
请返回JSON格式的匹配结果：
{{
    "matches": [
        {{"excel_index": 1, "csv_index": 2, "confidence": "high", "reason": "匹配理由"}}
    ],
    "unmatched_excel": [2],
    "unmatched_csv": [1, 3]
}}

只返回JSON，不要其他文字。"""
    
    print(f"\n=== 调用API ===")
    print(f"Prompt长度: {len(prompt)} 字符")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.ZHIPU_API_KEY}"
    }
    
    data = {
        "model": "glm-4-flash",  # 使用工作正常的模型
        "messages": [
            {
                "role": "system",
                "content": "你是专业的地理位置匹配专家。请严格按照JSON格式返回结果，不要添加任何其他文字。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 1000
    }
    
    try:
        print("🚀 正在调用API...")
        start_time = time.time()
        
        response = requests.post(
            config.BASE_URL,
            headers=headers,
            json=data,
            timeout=60
        )
        
        end_time = time.time()
        print(f"📡 响应时间: {end_time - start_time:.2f} 秒")
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result_data = response.json()
            content = result_data['choices'][0]['message']['content']
            
            print(f"✅ API调用成功!")
            print(f"📝 返回内容: {content}")
            
            # 尝试解析JSON
            try:
                # 清理可能的markdown格式
                json_content = content.strip()
                if json_content.startswith('```json'):
                    json_content = json_content[7:]
                if json_content.endswith('```'):
                    json_content = json_content[:-3]
                json_content = json_content.strip()
                
                match_result = json.loads(json_content)
                
                print(f"\n=== 匹配结果 ===")
                print(json.dumps(match_result, ensure_ascii=False, indent=2))
                
                # 显示匹配详情
                if 'matches' in match_result and match_result['matches']:
                    print(f"\n=== 匹配详情 ===")
                    for match in match_result['matches']:
                        excel_idx = match['excel_index'] - 1
                        csv_idx = match['csv_index'] - 1
                        
                        if excel_idx < len(excel_records) and csv_idx < len(csv_records):
                            excel_record = excel_records[excel_idx]
                            csv_record = csv_records[csv_idx]
                            
                            print(f"\n🎯 匹配 {match['excel_index']} -> {match['csv_index']}:")
                            print(f"   📍 积水点: {excel_record['point']}")
                            print(f"   🗺️  地址: {csv_record['road']}")
                            print(f"   🏢 POI: {csv_record['poi']}")
                            print(f"   📍 坐标: ({csv_record['lon']}, {csv_record['lat']})")
                            print(f"   🎯 置信度: {match['confidence']}")
                            print(f"   💭 理由: {match['reason']}")
                
                # 保存结果
                output_data = {
                    'excel_records': excel_records,
                    'csv_records': csv_records,
                    'match_result': match_result,
                    'prompt': prompt
                }
                
                output_file = f'工作演示结果_{district}.json'
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, ensure_ascii=False, indent=2)
                
                print(f"\n💾 结果已保存到: {output_file}")
                
                # 创建Excel结果文件
                create_excel_result(excel_df, csv_df, excel_test, csv_test, match_result, district)
                
                return match_result
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始内容: {content}")
                return None
                
        elif response.status_code == 429:
            print(f"❌ API调用失败: 余额不足或请求过于频繁")
            print(f"错误信息: {response.text}")
            return None
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_excel_result(excel_df, csv_df, excel_test, csv_test, match_result, district):
    """创建Excel结果文件"""
    
    try:
        # 创建结果DataFrame
        result_df = excel_test.copy()
        result_df['经度'] = None
        result_df['纬度'] = None
        result_df['道路地址'] = None
        result_df['POI地址'] = None
        result_df['匹配置信度'] = None
        result_df['匹配理由'] = None
        
        # 应用匹配结果
        if 'matches' in match_result:
            csv_list = csv_test.reset_index(drop=True)
            excel_list = excel_test.reset_index(drop=True)
            
            for match in match_result['matches']:
                excel_idx = match['excel_index'] - 1
                csv_idx = match['csv_index'] - 1
                
                if excel_idx < len(excel_list) and csv_idx < len(csv_list):
                    # 获取原始索引
                    excel_original_idx = excel_list.index[excel_idx]
                    csv_row = csv_list.iloc[csv_idx]
                    
                    # 更新结果
                    result_df.loc[excel_original_idx, '经度'] = csv_row['lon']
                    result_df.loc[excel_original_idx, '纬度'] = csv_row['lat']
                    result_df.loc[excel_original_idx, '道路地址'] = csv_row['road_address']
                    result_df.loc[excel_original_idx, 'POI地址'] = csv_row['poi_address']
                    result_df.loc[excel_original_idx, '匹配置信度'] = match['confidence']
                    result_df.loc[excel_original_idx, '匹配理由'] = match['reason']
        
        # 保存Excel文件
        output_excel = f'匹配结果_{district}.xlsx'
        result_df.to_excel(output_excel, index=False)
        print(f"📊 Excel结果已保存到: {output_excel}")
        
    except Exception as e:
        print(f"⚠️ 创建Excel文件失败: {e}")

if __name__ == "__main__":
    working_address_matching()
