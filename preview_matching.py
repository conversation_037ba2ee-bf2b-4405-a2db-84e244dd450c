import pandas as pd
import json
import requests
import time
import config
from datetime import datetime

def preview_matching():
    """预览模式：处理少量数据进行测试"""
    
    print("=== 智谱AI地址匹配预览模式 ===")
    print("🔍 这将处理少量数据来测试匹配效果和成本")
    
    # 检查配置
    if config.ZHIPU_API_KEY == "your-api-key-here":
        print("❌ 请先在config.py中设置API密钥")
        return
    
    # 读取数据
    try:
        excel_df = pd.read_excel(config.EXCEL_FILE)
        csv_df = pd.read_csv(config.CSV_FILE, encoding='gbk')
        print(f"✅ 数据加载成功: Excel {len(excel_df)} 条, CSV {len(csv_df)} 条")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 获取行政区信息
    excel_districts = set(excel_df['行政区'].dropna())
    csv_districts = set(csv_df['dist'].dropna())
    common_districts = excel_districts & csv_districts
    
    print(f"📍 共同行政区: {list(common_districts)}")
    
    # 选择一个数据较多的行政区进行测试
    district_counts = {}
    for district in common_districts:
        excel_count = len(excel_df[excel_df['行政区'] == district])
        csv_count = len(csv_df[csv_df['dist'] == district])
        district_counts[district] = min(excel_count, csv_count)
    
    # 选择数据最多的行政区
    test_district = max(district_counts.items(), key=lambda x: x[1])[0]
    print(f"🎯 选择测试行政区: {test_district}")
    
    # 获取测试数据
    excel_test = excel_df[excel_df['行政区'] == test_district].head(6)  # 测试6条Excel记录
    csv_test = csv_df[csv_df['dist'] == test_district].head(8)  # 8条CSV记录作为候选
    
    print(f"📊 测试数据: Excel {len(excel_test)} 条, CSV {len(csv_test)} 条")
    
    # 显示测试数据
    print(f"\n=== 测试数据预览 ===")
    print("📋 Excel记录:")
    for i, row in excel_test.iterrows():
        print(f"  {i+1}: {row['风险点']}")
    
    print("📍 CSV记录:")
    for i, row in csv_test.iterrows():
        print(f"  {i+1}: {row['road_address']} - {row['poi_address']}")
    
    # 估算成本
    batches = (len(excel_test) + config.BATCH_SIZE - 1) // config.BATCH_SIZE
    estimated_cost = batches * 0.25  # 每次API调用约0.25元
    print(f"\n💰 预估成本: {batches} 次API调用 ≈ ¥{estimated_cost:.2f}")
    
    # 确认继续
    user_input = input(f"\n是否继续预览测试？(y/n): ").lower().strip()
    if user_input != 'y':
        print("❌ 用户取消")
        return
    
    # 开始处理
    print(f"\n=== 开始预览匹配 ===")
    start_time = datetime.now()
    
    all_matches = []
    api_calls = 0
    
    # 分批处理
    for i in range(0, len(excel_test), config.BATCH_SIZE):
        batch_excel = excel_test.iloc[i:i+config.BATCH_SIZE]
        
        print(f"\n🔄 处理批次 {i//config.BATCH_SIZE + 1}/{batches}")
        
        # 准备数据
        excel_records = []
        for idx, row in batch_excel.iterrows():
            excel_records.append({
                'index': len(excel_records) + 1,
                'point': row['风险点'],
                'original_idx': idx
            })
        
        csv_records = []
        for idx, row in csv_test.iterrows():
            csv_records.append({
                'index': len(csv_records) + 1,
                'road': row['road_address'],
                'poi': row['poi_address'],
                'lon': row['lon'],
                'lat': row['lat'],
                'original_idx': idx
            })
        
        # 创建prompt
        prompt = create_preview_prompt(excel_records, csv_records, test_district)
        
        # 调用API
        result = call_api(prompt)
        api_calls += 1
        
        if 'error' in result:
            print(f"❌ 批次失败: {result['error']}")
            continue
        
        # 处理结果
        batch_matches = process_preview_result(result, excel_records, csv_records, excel_test, csv_test)
        all_matches.extend(batch_matches)
        
        # API调用间隔
        time.sleep(config.DELAY_BETWEEN_CALLS)
    
    # 显示结果
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\n=== 预览结果 ===")
    print(f"⏱️  耗时: {duration}")
    print(f"📡 API调用: {api_calls} 次")
    print(f"✅ 匹配数: {len(all_matches)}")
    print(f"📈 匹配率: {len(all_matches)/len(excel_test)*100:.1f}%")
    
    if all_matches:
        print(f"\n=== 匹配详情 ===")
        for i, match in enumerate(all_matches, 1):
            print(f"{i}. {match['excel_point']}")
            print(f"   -> {match['csv_road']}")
            print(f"   -> {match['csv_poi']}")
            print(f"   -> 坐标: ({match['csv_lon']}, {match['csv_lat']})")
            print(f"   -> 置信度: {match['confidence']}")
            print(f"   -> 理由: {match['reason']}")
            print()
    
    # 保存预览结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    preview_file = f'预览结果_{test_district}_{timestamp}.json'
    
    with open(preview_file, 'w', encoding='utf-8') as f:
        json.dump({
            'test_district': test_district,
            'excel_count': len(excel_test),
            'csv_count': len(csv_test),
            'matches': all_matches,
            'api_calls': api_calls,
            'duration': str(duration),
            'match_rate': len(all_matches)/len(excel_test)*100
        }, f, ensure_ascii=False, indent=2)
    
    print(f"💾 预览结果已保存: {preview_file}")
    
    # 全量处理建议
    total_excel = len(excel_df)
    estimated_batches = (total_excel + config.BATCH_SIZE - 1) // config.BATCH_SIZE
    estimated_full_cost = estimated_batches * 0.25
    
    print(f"\n=== 全量处理预估 ===")
    print(f"📊 总Excel记录: {total_excel}")
    print(f"📡 预估API调用: {estimated_batches} 次")
    print(f"💰 预估总成本: ¥{estimated_full_cost:.2f}")
    print(f"⏱️  预估耗时: {estimated_batches * 3 / 60:.1f} 分钟")
    
    print(f"\n如果预览结果满意，可以运行: python full_matching_solution.py")

def create_preview_prompt(excel_records, csv_records, district):
    """创建预览prompt"""
    
    prompt = f"""你是专业的地理位置匹配专家。请判断以下{district}的积水点与地图地址的匹配关系。

**积水点数据：**
"""
    
    for record in excel_records:
        prompt += f"{record['index']}. {record['point']}\n"
    
    prompt += f"\n**地图地址数据：**\n"
    
    for record in csv_records:
        prompt += f"{record['index']}. {record['road']} - {record['poi']}\n"
    
    prompt += f"""
**匹配原则：**
1. 优先考虑地理位置的实际对应关系
2. 理解"下穿"、"立交"、"交叉口"等不同表达方式
3. 只有高度确信是同一地点时才匹配

**返回JSON格式：**
```json
{{
    "matches": [
        {{"excel_index": 1, "csv_index": 3, "confidence": "high", "reason": "匹配理由"}}
    ],
    "unmatched_excel": [2],
    "unmatched_csv": [1, 2, 4, 5, 6, 7, 8]
}}
```

只返回JSON，不要其他文字。"""

    return prompt

def call_api(prompt):
    """调用API"""
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.ZHIPU_API_KEY}"
    }
    
    data = {
        "model": "glm-4-flash",
        "messages": [
            {
                "role": "system",
                "content": "你是专业的地理位置匹配专家。严格按照JSON格式返回结果。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 1000
    }
    
    try:
        response = requests.post(
            config.BASE_URL,
            headers=headers,
            json=data,
            timeout=60
        )
        
        if response.status_code == 200:
            result_data = response.json()
            content = result_data['choices'][0]['message']['content']
            
            # 解析JSON
            json_content = content.strip()
            if json_content.startswith('```json'):
                json_content = json_content[7:]
            if json_content.endswith('```'):
                json_content = json_content[:-3]
            json_content = json_content.strip()
            
            return json.loads(json_content)
        else:
            return {"error": f"API调用失败: {response.status_code}"}
            
    except Exception as e:
        return {"error": str(e)}

def process_preview_result(result, excel_records, csv_records, excel_df, csv_df):
    """处理预览结果"""
    
    matches = []
    
    if 'matches' in result:
        for match in result['matches']:
            excel_idx = match['excel_index'] - 1
            csv_idx = match['csv_index'] - 1
            
            if excel_idx < len(excel_records) and csv_idx < len(csv_records):
                excel_record = excel_records[excel_idx]
                csv_record = csv_records[csv_idx]
                
                match_info = {
                    'excel_point': excel_record['point'],
                    'csv_road': csv_record['road'],
                    'csv_poi': csv_record['poi'],
                    'csv_lon': csv_record['lon'],
                    'csv_lat': csv_record['lat'],
                    'confidence': match['confidence'],
                    'reason': match['reason']
                }
                
                matches.append(match_info)
                print(f"✅ 匹配: {excel_record['point']} -> {csv_record['road']} ({match['confidence']})")
    
    return matches

if __name__ == "__main__":
    preview_matching()
