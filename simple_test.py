import pandas as pd
import json
import requests
import time
import config

def simple_api_test():
    """简单的API连接测试"""
    
    print("=== 智谱AI API连接测试 ===")
    
    # 检查配置
    if config.ZHIPU_API_KEY == "your-api-key-here":
        print("❌ 请先在config.py中设置API密钥")
        return
    
    print(f"🔧 配置信息:")
    print(f"   API密钥: {config.ZHIPU_API_KEY[:20]}...")
    print(f"   模型: {config.MODEL_NAME}")
    print(f"   超时: {config.TIMEOUT}秒")
    
    # 测试网络连接
    try:
        print("\n🌐 测试基础网络连接...")
        response = requests.get("https://www.baidu.com", timeout=10)
        print(f"   ✅ 网络连接正常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ 网络连接失败: {e}")
        return
    
    # 测试智谱AI域名解析
    try:
        print("\n🔍 测试智谱AI域名解析...")
        response = requests.get("https://open.bigmodel.cn", timeout=10)
        print(f"   ✅ 智谱AI域名可访问 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ 智谱AI域名访问失败: {e}")
        print("   💡 可能需要检查防火墙或代理设置")
    
    # 简单的API调用测试
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.ZHIPU_API_KEY}"
    }
    
    # 使用非常简单的prompt进行测试
    simple_data = {
        "model": config.MODEL_NAME,
        "messages": [
            {
                "role": "user",
                "content": "请回答：1+1等于几？只回答数字。"
            }
        ],
        "temperature": 0.1,
        "max_tokens": 10
    }
    
    print(f"\n🚀 测试API调用...")
    print(f"   请求数据大小: {len(json.dumps(simple_data))} 字节")
    
    try:
        start_time = time.time()
        response = requests.post(
            config.BASE_URL,
            headers=headers,
            json=simple_data,
            timeout=config.TIMEOUT
        )
        end_time = time.time()
        
        print(f"   📡 响应时间: {end_time - start_time:.2f} 秒")
        print(f"   📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"   ✅ API调用成功!")
            print(f"   📝 返回内容: {content}")
            
            # 测试地址匹配
            print(f"\n🎯 测试地址匹配功能...")
            test_address_matching()
            
        else:
            print(f"   ❌ API调用失败")
            print(f"   📄 响应内容: {response.text}")
            
    except requests.exceptions.Timeout:
        print(f"   ⏰ API调用超时 (>{config.TIMEOUT}秒)")
        print(f"   💡 建议: 增加config.py中的TIMEOUT值")
        
    except requests.exceptions.ConnectionError as e:
        print(f"   🌐 连接错误: {e}")
        print(f"   💡 建议: 检查网络连接或代理设置")
        
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")

def test_address_matching():
    """测试地址匹配功能"""
    
    # 读取少量数据进行测试
    try:
        excel_df = pd.read_excel(config.EXCEL_FILE)
        csv_df = pd.read_csv(config.CSV_FILE, encoding='gbk')
        print(f"   📊 数据加载成功: Excel {len(excel_df)} 条, CSV {len(csv_df)} 条")
    except Exception as e:
        print(f"   ❌ 数据加载失败: {e}")
        return
    
    # 选择一个简单的测试案例
    district = '未央区'
    excel_sample = excel_df[excel_df['行政区'] == district].head(1)
    csv_sample = csv_df[csv_df['dist'] == district].head(2)
    
    if len(excel_sample) == 0 or len(csv_sample) == 0:
        print(f"   ❌ 未找到{district}的测试数据")
        return
    
    # 构建简化的匹配prompt
    excel_point = excel_sample.iloc[0]['风险点']
    csv_addr1 = csv_sample.iloc[0]['road_address']
    csv_addr2 = csv_sample.iloc[1]['road_address'] if len(csv_sample) > 1 else "无"
    
    simple_prompt = f"""请判断以下地址是否匹配，只返回JSON格式：

Excel: {excel_point}
CSV1: {csv_addr1}
CSV2: {csv_addr2}

返回格式：
{{"match": 1, "confidence": "high", "reason": "理由"}}

如果Excel与CSV1匹配，match返回1；与CSV2匹配，match返回2；都不匹配，match返回0。"""
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.ZHIPU_API_KEY}"
    }
    
    data = {
        "model": config.MODEL_NAME,
        "messages": [
            {
                "role": "system",
                "content": "你是地址匹配专家，只返回JSON格式结果。"
            },
            {
                "role": "user",
                "content": simple_prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 200
    }
    
    print(f"   📝 测试数据:")
    print(f"      Excel: {excel_point}")
    print(f"      CSV1: {csv_addr1}")
    print(f"      CSV2: {csv_addr2}")
    
    try:
        print(f"   🚀 调用地址匹配API...")
        start_time = time.time()
        response = requests.post(
            config.BASE_URL,
            headers=headers,
            json=data,
            timeout=config.TIMEOUT
        )
        end_time = time.time()
        
        print(f"   📡 响应时间: {end_time - start_time:.2f} 秒")
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"   ✅ 地址匹配测试成功!")
            print(f"   📝 返回内容: {content}")
            
            # 尝试解析JSON
            try:
                json_result = json.loads(content)
                print(f"   🎯 解析结果: {json_result}")
            except:
                print(f"   ⚠️ JSON解析失败，但API调用成功")
                
        else:
            print(f"   ❌ 地址匹配测试失败: {response.status_code}")
            print(f"   📄 错误内容: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 地址匹配测试出错: {e}")

if __name__ == "__main__":
    simple_api_test()
