# 智谱AI地址匹配工具 - 快速开始

## 🚀 快速开始

### 1. 设置API密钥

编辑 `config.py` 文件，将您的智谱AI API密钥填入：

```python
ZHIPU_API_KEY = "your-actual-api-key-here"
```

**获取API密钥**: https://open.bigmodel.cn/

### 2. 运行演示

```bash
python final_matching_script.py
```

### 3. 查看结果

程序会显示匹配过程和结果，并保存到JSON文件。

## 📁 推荐使用的文件

- **`final_matching_script.py`** - 主要演示脚本（推荐）⭐
- **`config.py`** - 配置文件（需要设置API密钥）
- `complete_matching_solution.py` - 完整解决方案
- `使用指南.md` - 详细说明

## 🎯 工作原理

1. **数据准备**: 读取Excel和CSV文件
2. **智能匹配**: 使用智谱AI GLM-4模型进行语义匹配
3. **结果处理**: 解析JSON结果并应用到数据中
4. **质量控制**: 提供置信度评估

## 💡 匹配示例

**Excel记录**: "元朔大道立交以北（下穿）"
**CSV记录**: "元朔大道与元光西路交叉口"
**匹配结果**: 高置信度匹配

## 💰 成本估算

- 单次API调用约 ¥0.25
- 处理298条记录预计 ¥15-20

## ⚠️ 注意事项

1. 确保网络连接正常
2. API密钥有足够余额
3. 对结果进行适当的人工验证

## 🔧 故障排除

如果遇到问题：
1. 检查API密钥是否正确设置
2. 确认网络连接正常
3. 查看错误日志信息
