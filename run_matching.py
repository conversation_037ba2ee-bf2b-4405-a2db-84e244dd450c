import pandas as pd
import json
import requests
from typing import List, Dict
import time
import os

class AddressMatcherWithLLM:
    def __init__(self, api_key: str = None, model: str = "glm-4"):
        """
        初始化地址匹配器
        
        Args:
            api_key: 智谱AI API密钥
            model: 使用的模型名称
        """
        self.api_key = api_key
        self.model = model
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"

    def create_matching_prompt(self, excel_records: List[Dict], csv_records: List[Dict]) -> str:
        """创建地址匹配的prompt"""
        
        prompt = """你是一个专业的地理位置匹配专家。请帮我判断以下两组地址数据中哪些是指向同一个地点的。

**任务说明：**
- 第一组数据来自积水点风险评估表，包含风险点描述
- 第二组数据来自地图服务，包含详细的道路地址和POI地址
- 请判断第一组中的每个风险点是否与第二组中的某个地点匹配

**匹配原则：**
1. 优先考虑地理位置的实际对应关系，而不是文字的完全一致
2. 考虑道路交叉口、下穿隧道、立交桥等交通设施的描述差异
3. 注意同一地点可能有不同的表达方式（如"下穿"vs"交叉口"）
4. 考虑行政区划的一致性
5. POI地址可以作为辅助判断依据

**第一组数据（风险点）：**
"""
        
        for i, record in enumerate(excel_records, 1):
            prompt += f"{i}. 行政区：{record['行政区']} | 风险点：{record['风险点']}\n"
        
        prompt += "\n**第二组数据（地图地址）：**\n"
        
        for i, record in enumerate(csv_records, 1):
            prompt += f"{i}. 行政区：{record['dist']} | 道路地址：{record['road_address']} | POI地址：{record['poi_address']} | 坐标：({record['lon']}, {record['lat']})\n"
        
        prompt += """
**请按以下JSON格式返回匹配结果：**
```json
{
    "matches": [
        {
            "excel_index": 1,
            "csv_index": 3,
            "confidence": "high",
            "reason": "两者都描述了同一个道路交叉口，只是表达方式略有不同"
        }
    ],
    "unmatched_excel": [2],
    "unmatched_csv": [1, 2],
    "summary": {
        "total_matches": 1,
        "high_confidence": 1,
        "medium_confidence": 0,
        "low_confidence": 0
    }
}
```

请仔细分析每个地点的特征，给出准确的匹配结果。只返回JSON，不要其他说明文字。"""

        return prompt

    def call_llm_api(self, prompt: str) -> Dict:
        """调用智谱AI API"""
        if not self.api_key:
            return {"error": "未提供API密钥"}
        
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的地理位置匹配专家，擅长分析和匹配不同来源的地址数据。请严格按照要求的JSON格式返回结果，不要添加任何其他文字。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 4096
            }
            
            print("正在调用智谱AI API...")
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result_data = response.json()
            content = result_data['choices'][0]['message']['content']
            print(f"API返回内容: {content[:200]}...")
            
            # 尝试提取JSON部分
            json_start = content.find('```json')
            json_end = content.find('```', json_start + 7)
            
            if json_start != -1 and json_end != -1:
                json_str = content[json_start + 7:json_end].strip()
                result = json.loads(json_str)
            else:
                # 如果没有找到代码块，尝试直接解析
                # 清理可能的前后缀
                content = content.strip()
                if content.startswith('```json'):
                    content = content[7:]
                if content.endswith('```'):
                    content = content[:-3]
                result = json.loads(content.strip())
            
            print("API调用成功，JSON解析完成")
            return result
            
        except requests.exceptions.RequestException as e:
            print(f"API请求失败: {e}")
            return {"error": f"API请求失败: {str(e)}"}
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            print(f"原始内容: {content}")
            return {"error": f"JSON解析失败: {str(e)}", "raw_response": content}
        except Exception as e:
            print(f"未知错误: {e}")
            return {"error": f"未知错误: {str(e)}"}

def run_single_batch_demo(api_key: str):
    """运行单批次演示"""
    
    # 读取数据
    excel_df = pd.read_excel('ocr表格1h201.9mm.xlsx')
    csv_df = pd.read_csv('积水点_带地址.csv', encoding='gbk')
    
    print(f"Excel数据: {len(excel_df)} 条记录")
    print(f"CSV数据: {len(csv_df)} 条记录")
    
    # 初始化匹配器
    matcher = AddressMatcherWithLLM(api_key=api_key)
    
    # 选择未央区的数据进行测试
    district = '未央区'
    excel_district = excel_df[excel_df['行政区'] == district].head(2)
    csv_district = csv_df[csv_df['dist'] == district].head(5)
    
    print(f"\n=== 测试数据 ({district}) ===")
    print("Excel记录:")
    for i, row in excel_district.iterrows():
        print(f"  {i}: {row['风险点']}")
    
    print("CSV记录:")
    for i, row in csv_district.iterrows():
        print(f"  {i}: {row['road_address']} - {row['poi_address']}")
    
    # 准备数据
    excel_records = []
    for idx, row in excel_district.iterrows():
        excel_records.append({
            'original_index': idx,
            '行政区': row['行政区'],
            '风险点': row['风险点'],
            '编号': row['编号']
        })
    
    csv_records = []
    for idx, row in csv_district.iterrows():
        csv_records.append({
            'original_index': idx,
            'dist': row['dist'],
            'road_address': row['road_address'],
            'poi_address': row['poi_address'],
            'lon': row['lon'],
            'lat': row['lat']
        })
    
    # 生成prompt并调用API
    prompt = matcher.create_matching_prompt(excel_records, csv_records)
    result = matcher.call_llm_api(prompt)
    
    print(f"\n=== 匹配结果 ===")
    if 'error' in result:
        print(f"错误: {result['error']}")
        if 'raw_response' in result:
            print(f"原始响应: {result['raw_response']}")
        return None
    
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 应用匹配结果
    result_df = excel_df.copy()
    result_df['经度'] = None
    result_df['纬度'] = None
    result_df['道路地址'] = None
    result_df['POI地址'] = None
    result_df['匹配置信度'] = None
    result_df['匹配理由'] = None
    
    if 'matches' in result:
        for match in result['matches']:
            excel_idx = match['excel_index'] - 1
            csv_idx = match['csv_index'] - 1
            
            if excel_idx < len(excel_records) and csv_idx < len(csv_records):
                excel_original_idx = excel_records[excel_idx]['original_index']
                csv_record = csv_records[csv_idx]
                
                result_df.loc[excel_original_idx, '经度'] = csv_record['lon']
                result_df.loc[excel_original_idx, '纬度'] = csv_record['lat']
                result_df.loc[excel_original_idx, '道路地址'] = csv_record['road_address']
                result_df.loc[excel_original_idx, 'POI地址'] = csv_record['poi_address']
                result_df.loc[excel_original_idx, '匹配置信度'] = match['confidence']
                result_df.loc[excel_original_idx, '匹配理由'] = match['reason']
                
                print(f"\n匹配成功:")
                print(f"  Excel: {excel_records[excel_idx]['风险点']}")
                print(f"  CSV: {csv_record['road_address']}")
                print(f"  置信度: {match['confidence']}")
                print(f"  理由: {match['reason']}")
    
    # 保存结果
    output_file = f'匹配结果_演示_{district}.xlsx'
    result_df.to_excel(output_file, index=False)
    print(f"\n结果已保存到: {output_file}")
    
    return result

def main():
    """主函数"""
    print("=== 智谱AI地址匹配演示 ===")
    
    # 检查API密钥
    api_key = input("请输入您的智谱AI API密钥: ").strip()
    
    if not api_key or api_key == 'your-api-key':
        print("错误: 请提供有效的API密钥")
        print("获取API密钥: https://open.bigmodel.cn/")
        return
    
    try:
        result = run_single_batch_demo(api_key)
        
        if result:
            print("\n=== 演示完成 ===")
            print("如需处理完整数据集，请修改代码中的批次大小和行政区范围")
        else:
            print("\n=== 演示失败 ===")
            print("请检查API密钥和网络连接")
            
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
